Public Sub CCC_Gliese_AssignGroup(ByVal UID_UNSAccount As String, ByVal UID_UNSGroup As String, ByVal UID_Object As String)

Dim user As String = UID_UNSAccount
Dim groups As String = UID_UNSGroup
Dim objectSS As String = UID_Object
If objectSS <> "" Then
	objectSS = objectSS.Replace("GLIESE_", "")
	user = objectSS
End If
' Define the JSON data to send  
'Dim jsonData As String = "{ ""usuario"": { ""identificacao"": " + user + ", ""menus"": [" + groups + "] } }" 
Dim jsonData As String = String.Format("{{ ""usuario"": {{ ""identificacao"": ""{0}"", ""menus"": [""{1}""] }} }}", user, groups) 
  
' Create a new WebClient object  
Dim client As New System.Net.WebClient()  
  
' Set the content type header to indicate JSON data  
client.Headers.Add("Content-Type", "application/json")
client.Headers.Add("apikey", "IQRpKNKodzLzAp02J1hwYCkBDRADCwXL")
  
' Send the POST request and get the response data  
Dim responseBytes As Byte() = client.UploadData("https://api.godigibee.io/pipeline/dasa/v1/gliese-idm", "POST", System.Text.Encoding.ASCII.GetBytes(jsonData))  
Dim responseString As String = System.Text.Encoding.ASCII.GetString(responseBytes)  
  
' Display the response data  
'MsgBox(responseString)  

End Sub
