Imports Newtonsoft.Json.Linq  
Imports Newtonsoft.Json

Public Sub CCC_Custom_Gliese_Update(ByVal identificacao As String, ByVal func As String, ByVal setor As String)
    Dim f = Session.SqlFormatter
    Dim identificacaoS As String = identificacao

    Dim personManagerAdsAccountQuery = f.And<PERSON>elation(f.Uid<PERSON>om<PERSON>ison("AccountName", identificacaoS), f.<PERSON>("UID_UNSRootB", "e8a88fd6-f552-4c93-9dfb-d7625ad5f93f"))
    
    Dim personManagerAdsAccountCol As IEntityCollection = Session.Source().GetCollection(Query.From("UNSAccountB").Where(personManagerAdsAccountQuery).SelectNonLobs)

    If personManagerAdsAccountCol IsNot Nothing AndAlso personManagerAdsAccountCol.Count > 0 Then
        Dim personManagerAdsAccount As IEntity = personManagerAdsAccountCol(0)

        Dim objectSS As String = personManagerAdsAccount.GetValue("ObjectGUID").String
        If objectSS <> "" Then
            objectSS = objectSS.Replace("GLIESE_", "")
            identificacaoS = objectSS
        End If

        Dim jsonData As String = String.Format("{{""isConsulta"" : ""true"", ""usuario"": {{ ""identificacao"": ""{0}""}} }}", identificacaoS)
        
        Dim client As New System.Net.WebClient()  
        client.Headers.Add("Content-Type", "application/json")
        client.Headers.Add("apikey", "o7Oj99AZ7WzkKr9SwM9AusS9f6GZElNj")
        
        Dim responseBytes As Byte() = client.UploadData("https://test.godigibee.io/pipeline/dasa/v1/gliese-usuario", "POST", System.Text.Encoding.ASCII.GetBytes(jsonData))  
        Dim responseString As String = System.Text.Encoding.ASCII.GetString(responseBytes)

        Dim jsonObj As JObject = JObject.Parse(responseString)  
        Dim identificacaoC As String = jsonObj.SelectToken("data[0].identificacao").ToString()

        Dim matriculaC As String = jsonObj.SelectToken("data[0].matricula").ToString()
        Dim cpfC As String = jsonObj.SelectToken("data[0].cpf").ToString()
        Dim nomeC As String = jsonObj.SelectToken("data[0].nome").ToString()
        Dim emailC As String = jsonObj.SelectToken("data[0].email").ToString()
        Dim funcaoC As String = func
        Dim setorC As String = setor
        Dim dataExpiracaoC As String = jsonObj.SelectToken("data[0].dataExpiracao").ToString()
        Dim empresaInternaC As String = jsonObj.SelectToken("data[0].empresaInterna").ToString()
        Dim mnemonicoPostoC As String = jsonObj.SelectToken("data[0].mnemonicoPosto").ToString()
        Dim unidadeRealizacaoExameC As String = jsonObj.SelectToken("data[0].unidadeRealizacaoExame").ToString()

        ' Nova tratativa para empresasIncorporadas (substitui o código antigo)
        Dim empresasIncorporadasJson As String = "[]"
        Try 
            Dim empresasToken As JToken = jsonObj.SelectToken("data[0].empresasIncorporadas")
            If empresasToken IsNot Nothing Then
                Dim empresasList As List(Of String) = If(empresasToken.Type = JTokenType.Array,
                    empresasToken.ToObject(Of List(Of String))(),
                    New List(Of String) From {empresasToken.ToString()}
                )
                empresasIncorporadasJson = JsonConvert.SerializeObject(empresasList.Distinct())
            End If
        Catch
            empresasIncorporadasJson = "[]"
        End Try

        jsonData = String.Format("{{ ""usuario"": {{ ""identificacao"": ""{0}"", ""matricula"": ""{1}"", ""nome"": ""{2}"", ""funcao"": ""{3}"", ""setor"": ""{4}"", ""unidadeRealizacaoExame"": ""{5}"", ""email"": ""{6}"", ""empresaInterna"": ""{7}"", ""cpf"": ""{8}"", ""mnemonicoPosto"": ""{9}"", ""dataExpiracao"": ""{10}"", ""empresasIncorporadas"": {11} }} }}", identificacaoS, matriculaC, nomeC, funcaoC, setorC, unidadeRealizacaoExameC, emailC, empresaInternaC, cpfC, mnemonicoPostoC, dataExpiracaoC, empresasIncorporadasJson) 

        Dim client2 As New System.Net.WebClient()
        client2.Headers.Add("Content-Type", "application/json")
        client2.Headers.Add("apikey", "o7Oj99AZ7WzkKr9SwM9AusS9f6GZElNj")
        
        Dim responseBytes2 As Byte() = client2.UploadData("https://test.godigibee.io/pipeline/dasa/v1/gliese-usuario", "PATCH", System.Text.Encoding.ASCII.GetBytes(jsonData))  
        Dim responseString2 As String = System.Text.Encoding.ASCII.GetString(responseBytes2) 
    End If
End Sub