WITH FilteredAccounts AS (
    -- Filtrar contas com mais de um grupo válido associado
    SELECT
        ag.UID_ADSAccount,
        COUNT(DISTINCT ag.UID_ADSGroup) AS GroupCount
    FROM
        ADSAccountInADSGroup ag
    WHERE
        ag.UID_ADSGroup IN (
            '7581fdfb-e5f2-4ee6-bf0b-c41492439bdb', 
            'e6ad1ae8-4f91-4883-a20c-77c926768722', 
            'd0e68a0d-be51-430d-a2a0-eb17225dbe23', 
            'fde33c0a-ea72-4497-9575-30a688af4c98'
        )
        AND ag.XMarkedForDeletion = 0 -- Considerar apenas licenças não marcadas para exclusão
        AND ag.XIsInEffect = 1       -- Considerar apenas licenças em vigor
    GROUP BY
        ag.UID_ADSAccount
    HAVING
        COUNT(DISTINCT ag.UID_ADSGroup) > 1 -- Contas com mais de um grupo
),
RankedGroups AS (
    -- Classificar os grupos válidos pela data de inserção
    SELECT
        ag.UID_ADSAccount,
        ag.UID_ADSGroup,
        ag.XDateInserted,
        ROW_NUMBER() OVER (PARTITION BY ag.UID_ADSAccount ORDER BY ag.XDateInserted DESC) AS RankPosition
    FROM
        ADSAccountInADSGroup ag
    WHERE
        ag.UID_ADSAccount IN (SELECT UID_ADSAccount FROM FilteredAccounts)
        AND ag.UID_ADSGroup IN (
            '7581fdfb-e5f2-4ee6-bf0b-c41492439bdb', 
            'e6ad1ae8-4f91-4883-a20c-77c926768722', 
            'd0e68a0d-be51-430d-a2a0-eb17225dbe23', 
            'fde33c0a-ea72-4497-9575-30a688af4c98'
        )
        AND ag.XMarkedForDeletion = 0 -- Reaplicar filtro
        AND ag.XIsInEffect = 1        -- Reaplicar filtro
)
-- Selecionar apenas o grupo mais recente para cada conta, trazendo o SAMAccountName e CN do grupo
SELECT
    a.SAMAccountName,
    g.CN AS GroupCN, -- Nome do grupo
    rg.XDateInserted AS LicenseInsertedDate
FROM
    RankedGroups rg
JOIN
    ADSAccount a ON rg.UID_ADSAccount = a.UID_ADSAccount -- Relacionando com ADSAccount para obter SAMAccountName
JOIN
    ADSGroup g ON rg.UID_ADSGroup = g.UID_ADSGroup -- Relacionando com ADSGroup para obter CN (nome do grupo)
WHERE
    rg.RankPosition = 1 -- Apenas o grupo mais recente para cada conta
ORDER BY
    a.SAMAccountName;
