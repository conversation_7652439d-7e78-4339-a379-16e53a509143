((CustomProperty04 = 'Estagiários') and 
IdentityType = 'Primary' and 
IsExternal = '0' and
IsInActive = '0' and
EntryDate >= '02/10/25' and
not exists (select 1 from PersonWantsOrg 
    where PersonWantsOrg.UID_PersonOrdered = person.uid_person
    and DisplayOrg like 'Licença%' 
    and OrderState = 'Assigned'
    and OrderDate >= '2025-04-30')
or
(CustomProperty04 = 'Residente/Especializ' or 
CustomProperty04 = 'Aprendiz') and 
IdentityType = 'Primary' and 
IsExternal = '0' and
IsInActive = '0' and
EntryDate >= '02/10/25' and
not exists (select 1 from PersonWantsOrg 
    where PersonWantsOrg.UID_PersonOrdered = person.uid_person
    and DisplayOrg like 'Licença%' 
    and OrderState = 'Assigned'
    and OrderDate >= '2025-04-30')
or
(CustomProperty04 = 'Op Administrativos') and 
CustomProperty05 = 'Analista' and
IdentityType = 'Primary' and 
IsExternal = '0' and
IsInActive = '0' and
EntryDate >= '02/10/25' and
not exists (select 1 from PersonWantsOrg 
    where PersonWantsOrg.UID_PersonOrdered = person.uid_person
    and DisplayOrg like 'Licença%' 
    and OrderState = 'Assigned'
    and OrderDate >= '2025-04-30'))
or
(CCC_ControleInterno = 'Office')