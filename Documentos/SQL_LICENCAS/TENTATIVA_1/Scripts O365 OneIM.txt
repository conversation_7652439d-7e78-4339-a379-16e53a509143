E1

-- Consulta para validar funcionários e estagiários que atendem a critérios específicos de cargos e grupos
(
    -- Filtro para funcionários administrativos e estagiários
    (CustomProperty04 IN ('Op Administrativos', 'Estagiários')) -- Cargos permitidos
    AND IdentityType = 'Primary' -- Identidade principal (não secundária)
    AND IsExternal = '0' -- Funcionário interno (não é externo)
    AND IsInActive = '0' -- Somente funcionários ativos
    AND EntryDate >= '2024-02-01' -- Data de entrada igual ou posterior a 1º de fevereiro de 2024
    
    -- Verifica se o funcionário **não possui** uma licença atribuída
    AND NOT EXISTS (
        SELECT 1 
        FROM PersonWantsOrg 
        WHERE PersonWantsOrg.UID_PersonOrdered = person.UID_Person
          AND DisplayOrg LIKE 'Licença%' -- Verifica se há licença no DisplayOrg
          AND OrderState = 'Assigned' -- <PERSON>cença atribuída (Assigned)
    )
    
    -- Garante que o funcionário **não pertence** aos grupos E3 ou F1
    AND NOT EXISTS (
        SELECT 1 
        FROM PersonInOrg 
        WHERE PersonInOrg.UID_Person = person.UID_Person
          AND UID_Org IN ('809d5640-85ae-4d32-955e-3c10d229f2f0', '5aa7f224-5f34-4d42-8ee3-fe55c8042233') -- IDs de E3 e F1
    )
)
OR
(
    -- Filtro para residentes/especialistas e aprendizes
    (CustomProperty04 IN ('Residente/Especializ', 'Aprendiz')) -- Cargos permitidos
    AND IdentityType = 'Primary' -- Identidade principal (não secundária)
    AND IsExternal = '0' -- Funcionário interno (não externo)
    AND IsInActive = '0' -- Somente funcionários ativos
    AND EntryDate >= '2024-05-28' -- Data de entrada igual ou posterior a 28 de maio de 2024
    
    -- Verifica se o funcionário **não possui** uma licença atribuída
    AND NOT EXISTS (
        SELECT 1 
        FROM PersonWantsOrg 
        WHERE PersonWantsOrg.UID_PersonOrdered = person.UID_Person
          AND DisplayOrg LIKE 'Licença%' -- Verifica se há licença no DisplayOrg
          AND OrderState = 'Assigned' -- Licença atribuída (Assigned)
    )
    
    -- Garante que o funcionário **não pertence** aos grupos E3 ou F1
    AND NOT EXISTS (
        SELECT 1 
        FROM PersonInOrg 
        WHERE PersonInOrg.UID_Person = person.UID_Person
          AND UID_Org IN ('809d5640-85ae-4d32-955e-3c10d229f2f0', '5aa7f224-5f34-4d42-8ee3-fe55c8042233') -- IDs de E3 e F1
    )
)

---------------------------------------------------------------------------------------

E3

-- Consulta para validar funcionários em cargos executivos ou de liderança que não têm licença e não estão em grupos específicos
(
    -- Filtra funcionários em cargos específicos
    (CustomProperty04 IN ('Executivos', 'Expatriados', 'Supervisão', 'Coordenação', 'Gerência')) -- Cargos permitidos
    AND IdentityType = 'Primary' -- Verifica se a identidade é principal (não uma identidade secundária)
    AND IsExternal = '0' -- Funcionário interno (não terceirizado)
    AND IsInActive = '0' -- Apenas funcionários ativos (não inativos)
    AND EntryDate >= '2024-02-01' -- Data de entrada igual ou posterior a 1º de fevereiro de 2024
    
    -- Verifica se o funcionário **não possui** uma licença atribuída
    AND NOT EXISTS (
        SELECT 1 
        FROM PersonWantsOrg 
        WHERE PersonWantsOrg.UID_PersonOrdered = person.UID_Person
          AND DisplayOrg LIKE 'Licença%' -- Organizações relacionadas a licenças
          AND OrderState = 'Assigned' -- Licença foi atribuída (Assigned)
    )
    
    -- Garante que o funcionário **não pertence** aos grupos E1 ou F1
    AND NOT EXISTS (
        SELECT 1 
        FROM PersonInOrg 
        WHERE PersonInOrg.UID_Person = person.UID_Person
          AND UID_Org IN ('c3c167f0-680f-4147-ac9d-67df354bd740', '5aa7f224-5f34-4d42-8ee3-fe55c8042233') -- Grupos E1 e F1
    )
)


---------------------------------------------------------------------------------------

F1

-- Consulta para validar funcionários e terceiros com critérios específicos
(
    -- Valida funcionários com cargos "Op Técnicos" ou "Residente/Especializ"
    (CustomProperty04 = 'Op Técnicos' OR CustomProperty04 = 'Residente/Especializ') 
    AND IdentityType = 'Primary' -- Identidade principal (não é uma identidade secundária)
    AND IsExternal = '0' -- Não é um externo (funcionário interno)
    AND IsInActive = '0' -- Ativo (não está inativo)
    AND EntryDate >= '2024-02-01' -- Entrada após ou em 1º de fevereiro de 2024
    -- Verifica se a pessoa **não tem** licença atribuída
    AND NOT EXISTS (
        SELECT 1 
        FROM PersonWantsOrg 
        WHERE PersonWantsOrg.UID_PersonOrdered = person.UID_Person
          AND DisplayOrg LIKE 'Licença%' -- Organizações relacionadas a licença
          AND OrderState = 'Assigned' -- Estado da licença é "Assigned" (atribuída)
    )
    -- Verifica se a pessoa **não pertence** aos grupos E1 ou E3
    AND NOT EXISTS (
        SELECT 1 
        FROM PersonInOrg 
        WHERE PersonInOrg.UID_Person = person.UID_Person
          AND UID_Org IN ('c3c167f0-680f-4147-ac9d-67df354bd740', '809d5640-85ae-4d32-955e-3c10d229f2f0') -- IDs de E1 ou E3
    )
)
OR
(
    -- Valida terceiros (externos) que **não solicitaram** licença
    IdentityType = 'Primary' -- Identidade principal
    AND IsExternal = '1' -- Externo (terceiro)
    AND IsInActive = '0' -- Ativo (não está inativo)
    AND EntryDate >= '2024-02-01' -- Entrada após ou em 1º de fevereiro de 2024
    -- Verifica se o terceiro **não solicitou** uma licença
    AND NOT EXISTS (
        SELECT 1 
        FROM PersonWantsOrg 
        WHERE PersonWantsOrg.UID_PersonOrdered = person.UID_Person
          AND DisplayOrg LIKE 'Licença%' -- Organizações relacionadas a licença
          AND OrderState = 'Assigned' -- Licença atribuída
    )
    -- Verifica se o terceiro **não pertence** aos grupos E1 ou E3
    AND NOT EXISTS (
        SELECT 1 
        FROM PersonInOrg 
        WHERE PersonInOrg.UID_Person = person.UID_Person
          AND UID_Org IN ('c3c167f0-680f-4147-ac9d-67df354bd740', '809d5640-85ae-4d32-955e-3c10d229f2f0') -- IDs de E1 ou E3
    )
)
OR
(
    -- Valida funcionários que **não possuem** E1/E3 e **não solicitaram** licença
    IdentityType = 'Primary' -- Identidade principal
    AND IsExternal = '0' -- Interno (funcionário)
    AND IsInActive = '0' -- Ativo (não está inativo)
    AND EntryDate >= '2024-02-01' -- Entrada após ou em 1º de fevereiro de 2024
    -- Verifica se o funcionário **não solicitou** uma licença
    AND NOT EXISTS (
        SELECT 1 
        FROM PersonWantsOrg 
        WHERE PersonWantsOrg.UID_PersonOrdered = person.UID_Person
          AND DisplayOrg LIKE 'Licença%' -- Organizações relacionadas a licença
          AND OrderState = 'Assigned' -- Licença atribuída
    )
    -- Verifica se o funcionário **não pertence** aos grupos E1 ou E3
    AND NOT EXISTS (
        SELECT 1 
        FROM PersonInOrg 
        WHERE PersonInOrg.UID_Person = person.UID_Person
          AND UID_Org IN ('c3c167f0-680f-4147-ac9d-67df354bd740', '809d5640-85ae-4d32-955e-3c10d229f2f0') -- IDs de E1 ou E3
    )
)


----------------------------------------------------------

HOMOL

E1 c3c167f0-680f-4147-ac9d-67df354bd740
E3 809d5640-85ae-4d32-955e-3c10d229f2f0
F1 5aa7f224-5f34-4d42-8ee3-fe55c8042233

PROD

E1 aadf5450-1899-40df-bbc9-733b3215b997
E3 5ee40f26-37ce-4ce6-9f72-07e1c30a7a4b
F1 85171ec1-c0a8-4844-a4f7-ab185199d6e1

----------------------------------------------------------CÓDIGOS ORIGINAIS

E1

(CustomProperty04 = 'Op Administrativos' or 
CustomProperty04 = 'Estagiários') and 
IdentityType = 'Primary' and 
IsExternal = '0' and
IsInActive = '0' and
EntryDate >= '02/01/24' and
not exists (select 1 from PersonWantsOrg 
	where PersonWantsOrg.UID_PersonOrdered = person.uid_person
	and DisplayOrg like 'Licença%' 
	and OrderState = 'Assigned')
	-- Condição para garantir que não esteja em outro grupo
    and not exists (
        select 1 from PersonInOrg 
        where PersonInOrg.UID_Person = person.uid_person 
        and UID_Org in ('809d5640-85ae-4d32-955e-3c10d229f2f0', '5aa7f224-5f34-4d42-8ee3-fe55c8042233')
    )
or
(CustomProperty04 = 'Residente/Especializ' or 
CustomProperty04 = 'Aprendiz') and 
IdentityType = 'Primary' and 
IsExternal = '0' and
IsInActive = '0' and
EntryDate >= '05/28/24' and
not exists (select 1 from PersonWantsOrg 
	where PersonWantsOrg.UID_PersonOrdered = person.uid_person
	and DisplayOrg like 'Licença%' 
	and OrderState = 'Assigned')
-- Condição para garantir que não esteja em outro grupo
    and not exists (
        select 1 from PersonInOrg 
        where PersonInOrg.UID_Person = person.uid_person 
        and UID_Org in ('809d5640-85ae-4d32-955e-3c10d229f2f0', '5aa7f224-5f34-4d42-8ee3-fe55c8042233')
    )
    
----------------------------------------------------------

E3

(CustomProperty04 = 'Executivos' or 
CustomProperty04 = 'Expatriados' or 
CustomProperty04 = 'Supervisão' or 
CustomProperty04 = 'Coordenação' or 
CustomProperty04 = 'Gerência') and 
IdentityType = 'Primary' and 
IsExternal = '0' and
IsInActive = '0' and
EntryDate >= '02/01/24' and
not exists (select 1 from PersonWantsOrg 
	where PersonWantsOrg.UID_PersonOrdered = person.uid_person
	and DisplayOrg like 'Licença%' 
	and OrderState = 'Assigned')
and not exists (
        select 1 from PersonInOrg 
        where PersonInOrg.UID_Person = person.uid_person 
        and UID_Org in ('c3c167f0-680f-4147-ac9d-67df354bd740', '5aa7f224-5f34-4d42-8ee3-fe55c8042233')
    )
    
----------------------------------------------------------

F1

--Valida Funcionarios com cargos especificos
(CustomProperty04 = 'Op Técnicos' or 
CustomProperty04 = 'Residente/Especializ') and 
IdentityType = 'Primary' and 
IsInActive = '0' and
IsExternal = '0' and
EntryDate >= '02/01/24' and
not exists (select 1 from PersonWantsOrg 
	where PersonWantsOrg.UID_PersonOrdered = person.uid_person
	and DisplayOrg like 'Licença%' 
	and OrderState = 'Assigned') 
	-- Condição para garantir que não esteja em outro grupo
    and not exists (
        select 1 from PersonInOrg 
        where PersonInOrg.UID_Person = person.uid_person 
        and UID_Org in ('c3c167f0-680f-4147-ac9d-67df354bd740', '809d5640-85ae-4d32-955e-3c10d229f2f0')
    )
--Valida Terceiros que não solicitaram licença
or
IdentityType = 'Primary' and 
IsExternal = '1' and
IsInActive = '0' and
EntryDate >= '02/01/24' and
not exists (select 1 from PersonWantsOrg 
	where PersonWantsOrg.UID_PersonOrdered = person.uid_person
	and DisplayOrg like 'Licença%' 
	and OrderState = 'Assigned') 
	-- Condição para garantir que não esteja em outro grupo
    and not exists (
        select 1 from PersonInOrg 
        where PersonInOrg.UID_Person = person.uid_person 
        and UID_Org in ('c3c167f0-680f-4147-ac9d-67df354bd740', '809d5640-85ae-4d32-955e-3c10d229f2f0')
    )
--Valida funcionarios que não receberam E1/E3 e não solicitaram licença
or 
IdentityType = 'Primary' and 
IsInActive = '0' and
IsExternal = '0' and
EntryDate >= '02/01/24' and
not exists (select 1 from PersonWantsOrg 
	where PersonWantsOrg.UID_PersonOrdered = person.uid_person
	and DisplayOrg like 'Licença%' 
	and OrderState = 'Assigned') 
and 
not exists (select 1 from PersonInOrg 
	where PersonInOrg.UID_Person = person.uid_person
	and (uid_org = 'c3c167f0-680f-4147-ac9d-67df354bd740' or uid_org = '809d5640-85ae-4d32-955e-3c10d229f2f0'))
	-- Condição para garantir que não esteja em outro grupo
    and not exists (
        select 1 from PersonInOrg 
        where PersonInOrg.UID_Person = person.uid_person 
        and UID_Org in ('c3c167f0-680f-4147-ac9d-67df354bd740', '809d5640-85ae-4d32-955e-3c10d229f2f0')
    )
    
    
    
-- Verificar sobre a possibilidade de retirar as licenças que contém Dynamic Roles;
-- Entender sobre porque os outros acessos são disponibilizados para solicitação mas que apresentam um público específico de identidades;
-- Plausível granular melhor as licenças disponibilizadas para solicitação?
-- Realização da retirada de solicitação dos demais acessos para que só seja disponibilizada;
-- Criação de um alerta caso o usuário 

-- Criação de documentação de GMUD
-- Especificação Funcional
-- Especificação Técnica
-- Casos de Testes
-- Evidências de Testes
-- Plano de RollOut

-- select * from AdsAccountInADSGroup where UID_ADSGroup in ('7581fdfb-e5f2-4ee6-bf0b-c41492439bdb','d0e68a0d-be51-430d-a2a0-eb17225dbe23','aadf5450-1899-40df-bbc9-733b3215b997')

-- Consultar com a Dandara e líderes técnicos sobre a melhoria/correção e entender qual é a melhor abordagem do problema

-- 
Estamos com um ticket aberto aqui na Dasa (https://sec4youhelp.zendesk.com/agent/tickets/44317) que conseguimos realizar ajustes no ambiente de teste que foram concluídos com êxito. Foi necessário mexer em scripts, e foram mudanças relativamente simples. Mas entendemos que o impacto desse processo, caso falhe em PRD, pode ter um impacto alto, retirando acesso de produtos Office dos usuários.

Queríamos entender qual a melhor abordagem em relação ao MSS. Deveríamos entender como uma melhoria complexa? Como podemos dar continuidade para a implementação dessa melhoria no ambiente?
