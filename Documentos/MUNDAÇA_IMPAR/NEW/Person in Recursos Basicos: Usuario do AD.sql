(isnull(cast(EntryDate as date), '1899-12-30 00:00:00.000') <= cast(GETDATE() as date) OR ( EXISTS ( SELECT 1 FROM  (SELECT UID_Person FROM ADSAccount WHERE 1 = 1) as X  WHERE X.UID_Person = Person.UID_Person  ) )) and (isexternal = 0) and (IdentityType = N'Primary')and 
isnull(companymember, 'xx') not in ('HOSPITAL SÃO LUCAS','MATERNIDADE BRASÍLIA','HOSPITAL BRASÍLIA','HOSPITAL ÁGUAS CLARA','HOSPITAL SANTA PAULA','H9J','IMPAR','CHN','SANTA CELINA GI','SAÚDE CELINA','INNOVA')