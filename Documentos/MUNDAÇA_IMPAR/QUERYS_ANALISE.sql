--Regras que tem IMPAR sendo analisado no campo companymember
select objectkeybasetree, displayname, whereclause from dynamicgroup where whereclause like ('%companymember%') and whereclause not like ('%1=0%') and whereclause like ('%IMPAR%')

--Usuários com IMPAR no campo companymember ativos
select * from person where companymember = 'IMPAR' and isinactive = 0

--<PERSON>ra "Usuário do AD" sem IMPAR
select * from person where (isnull(cast(EntryDate as date), '1899-12-30 00:00:00.000') <= cast(GETDATE() as date) OR ( EXISTS ( SELECT 1 FROM  (SELECT UID_Person FROM ADSAccount WHERE 1 = 1) as X  WHERE X.UID_Person = Person.UID_Person  ) )) and (isexternal = 0) and (IdentityType = N'Primary')and 
isnull(companymember, 'xx') not in ('HOSPITAL SÃO LUCAS','MATERNIDADE BRASÍLIA','HOSPITAL BRASÍLIA','HOSPITAL ÁGUAS CLARA','HOSPITAL SANTA PAULA','H9J','CHN','SANTA CELINA GI','SAÚDE CELINA','INNOVA')

--Usuários que tenha qualquer uma das regras que analisa o IMPAR. Resultado retorna majoritariamente regra "Usuário AD Hospitais"
SELECT 
	p.centralaccount,
	po.uid_org
FROM person p
INNER JOIN personinorg po ON p.uid_person = po.uid_person
WHERE p.companymember = 'IMPAR' 
  AND p.isinactive = 0
  AND po.uid_org IN (
    '377f6b81-85a1-440f-9d5d-2d88155a33eb',
    'a1e3bef1-d340-44cf-8167-4a5bcf82be3f',
    '21e6dbfa-b4dd-40a7-adda-9b6cc455e8bd',
    'ec0ffd5f-e53c-4881-a257-4b0f845aff0c',
    '1a450797-9ca0-45ab-be4a-51e4b8c842af'
  );

--Usuários com grupo "Usuário AD Hospitais" com IMPAR
SELECT 
	p.centralaccount,
	po.uid_org
FROM person p
INNER JOIN personinorg po ON p.uid_person = po.uid_person
WHERE p.companymember = 'IMPAR' 
  AND p.isinactive = 0
  AND po.uid_org IN (
    '377f6b81-85a1-440f-9d5d-2d88155a33eb'
  );

