Imports Newtonsoft.Json.Linq ' Adicione esta linha para usar JObject

Public Sub CCC_Motion_CreateUser(ByVal nome As String, ByVal login As String, ByVal email As String, ByVal grupoUsuarios As String, ByVal dataNasc As String, ByVal sexo As String, ByVal uf As String, ByVal localidade As String, ByVal cpf As String, ByVal cargo As String)
    
    ' Criar o JSON com as informações do usuário
    Dim jsonData As String = String.Format("{{
        ""Nome"": ""{0}"",
        ""Login"": ""{1}"",
        ""Email"": ""{2}"",
        ""Tipo de Autenticação"": ""Corporativa"",
        ""Está ativo?"": true,
        ""Grupo de Usuários"": ""{3}"",
        ""Data Nasc."": ""{4}"",
        ""Sexo"": ""{5}"",
        ""UF Endereço"": ""{6}"",
        ""Localidade"": ""{7}"",
        ""Tipo Documento"": ""CPF"",
        ""Nº Documento"": ""{8}"",
        ""Cargo"": ""{9}""
    }}", nome, login, email, grupoUsuarios, dataNasc, sexo, uf, localidade, cpf, cargo)
    
	' Configurar Headers
	Dim client As New System.Net.WebClient()
	Dim token As String = Connection.GetConfigParm("Custom\CustomTargetSystem\UniversidadeDASA\Apikey")
    client.Headers.Add("Content-Type", "application/json")
    client.Headers.Add("Authorization", "Bearer " + token)
	
    ' Enviar o JSON para o serviço Web Motion
    
    Try
        Dim responseBytes As Byte() = client.UploadData("https://api.motion.com/createAccount", "POST", System.Text.Encoding.ASCII.GetBytes(jsonData))
        Dim responseString As String = System.Text.Encoding.ASCII.GetString(responseBytes)
        
        ' Tratar a resposta do servidor
        Console.WriteLine("Resposta do servidor: " + responseString)
    Catch ex As Exception
        ' Tratar exceção
        Console.WriteLine("Erro ao enviar dados: " + ex.Message)
    End Try
End Sub





''''''




Imports Newtonsoft.Json.Linq ' Adicione esta linha para usar JObject

Public Function GetAuthToken(ByVal username As String, ByVal password As String) As String
    Dim client As New System.Net.WebClient()
    Dim credentials As String = Convert.ToBase64String(System.Text.Encoding.ASCII.GetBytes(username + ":" + password))
    client.Headers.Add("Authorization", "Basic " + credentials)
    
    Try
        Dim responseBytes As Byte() = client.DownloadData("https://api.motion.com/auth")
        Dim responseString As String = System.Text.Encoding.ASCII.GetString(responseBytes)
        
        ' Usar JObject para evitar late binding
        Dim jsonResponse As JObject = JObject.Parse(responseString)
        Return jsonResponse("token").ToString() ' Converter explicitamente para String
    Catch ex As Exception
        ' Tratar exceção
        Console.WriteLine("Erro ao obter token: " + ex.Message)
        Return Nothing
    End Try
End Function

Public Sub CCC_Motion_CreateAccount(ByVal identificacao As String, ByVal matricula As String, ByVal nome As String, ByVal funcao As String, ByVal setor As String, ByVal posto As String, ByVal unidadeRealizacaoExame As String, ByVal email As String, ByVal empresa As String, ByVal cpf As String)
    ' Obter o token de autenticação
    Dim token As String = GetAuthToken(Connection.GetConfigParm("Custom\CustomTargetSystem\Motion\username"), Connection.GetConfigParm("Custom\CustomTargetSystem\Motion\password"))
    
    If String.IsNullOrEmpty(token) Then
        ' Tratar erro de autenticação
        Console.WriteLine("Erro: Falha ao obter o token de autenticação.")
        Return
    End If
    
    ' Criar o JSON com as informações do usuário
    Dim jsonData As String = String.Format("{{
        ""Nome"": ""{0}"",
        ""Login"": ""{1}"",
        ""Email"": ""{2}"",
        ""Tipo de Autenticação"": ""Corporativa"",
        ""Está ativo?"": true,
        ""Grupo de Usuários"": ""grupo"",
        ""Data Nasc."": ""27/02/1995"",
        ""Sexo"": ""M"",
        ""UF Endereço"": ""SP"",
        ""Localidade"": ""São Paulo"",
        ""Tipo Documento"": ""CPF"",
        ""Nº Documento"": {3},
        ""Cargo"": ""{4}""
    }}", nome, identificacao, email, cpf, funcao)
    
    ' Enviar o JSON para o serviço Web Motion
    Dim client As New System.Net.WebClient()
    client.Headers.Add("Content-Type", "application/json")
    client.Headers.Add("Authorization", "Bearer " + token)
    
    Try
        Dim responseBytes As Byte() = client.UploadData("https://api.motion.com/createAccount", "POST", System.Text.Encoding.ASCII.GetBytes(jsonData))
        Dim responseString As String = System.Text.Encoding.ASCII.GetString(responseBytes)
        
        ' Tratar a resposta do servidor
        Console.WriteLine("Resposta do servidor: " + responseString)
    Catch ex As Exception
        ' Tratar exceção
        Console.WriteLine("Erro ao enviar dados: " + ex.Message)
    End Try
End Sub