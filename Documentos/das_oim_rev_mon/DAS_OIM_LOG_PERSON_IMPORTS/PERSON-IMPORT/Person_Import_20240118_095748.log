﻿2024-01-18 10:00:52 -03:00 - Serious -     Error in line 8137: [810457] Error saving Person  ()
[810306] Error running 'SetValues' in logic module 'VI.DB.Entities.EntityScriptLogic'.
[810126] Identities: <PERSON>rror applying dynamic template for Preferred name on object  ().
[810222] Error running script 'Tmpl_Person_PreferredName'.
[System.ArgumentOutOfRangeException] Index and length must refer to a location within the string.
Parameter name: length
2024-01-18 10:00:52 -03:00 - Serious -     Line data was: "126305";"<PERSON>rrany";"Costa Boiba Moura";"2";"";"<PERSON><PERSON><PERSON> Boiba Moura";"Estagiario Sem Bolsa";"2";"1992-12-02 00:00:00";"0";"50009190";"I5781018";"2023-12-18 00:00:00";"9999-12-31 00:00:00";"Impar Ser<PERSON><PERSON>";"118128";"0291";"BR";"";"61986390540";"<EMAIL>";"Hospital Águas Clara";"026.826.931-99";"2950272";"Estagiários";"Estagiário superior";"Mercado Hospit DASA";"Brasília";"2023-12-14 20:46:34";"1"
2024-01-18 10:00:59 -03:00 - Serious -     Error in line 8539: [810457] Error saving Person  ()
[810306] Error running 'SetValues' in logic module 'VI.DB.Entities.EntityScriptLogic'.
[810126] Identities: Error applying dynamic template for Preferred name on object  ().
[810222] Error running script 'Tmpl_Person_PreferredName'.
[System.ArgumentOutOfRangeException] Index and length must refer to a location within the string.
Parameter name: length
2024-01-18 10:00:59 -03:00 - Serious -     Line data was: "125944";"Jordana";"Gomes De Oliveira";"2";"";"Jordana  Gomes De Oliveira";"Estagiario Sem Bolsa";"2";"1995-03-22 00:00:00";"0";"50009242";"I5781286";"2023-12-09 00:00:00";"9999-12-31 00:00:00";"Impar Serv.Hospitalares";"69259";"0291";"BR";"";"61991162406";"<EMAIL>";"Hospital Águas Clara";"032.181.241-74";"3075741";"Estagiários";"Estagiário superior";"Mercado Hospit DASA";"Brasília";"2023-12-04 20:36:37";"1"
2024-01-18 10:01:19 -03:00 - Serious -     Error in line 9504: [810457] Error saving Person  ()
[810306] Error running 'SetValues' in logic module 'VI.DB.Entities.EntityScriptLogic'.
[810126] Identities: Error applying dynamic template for Preferred name on object  ().
[810222] Error running script 'Tmpl_Person_PreferredName'.
[System.ArgumentOutOfRangeException] Index and length must refer to a location within the string.
Parameter name: length
2024-01-18 10:01:19 -03:00 - Serious -     Line data was: "125809";"Bruno";"Coimbras de Almeida";"1";"";"Bruno  Coimbras de Almeida";"Estagiario Sem Bolsa";"1";"1990-02-23 00:00:00";"0";"50009206";"I5781457";"2023-12-04 00:00:00";"9999-12-31 00:00:00";"Impar Serv.Hospitalares";"69425";"0291";"BR";"";"61994361578";"<EMAIL>";"Hospital Águas Clara";"032.569.761-23";"3142376";"Estagiários";"Estagiário superior";"Mercado Hospit DASA";"Brasília";"2023-11-29 20:43:04";"1"
2024-01-18 10:01:49 -03:00 - Serious -     Error in line 10844: [810457] Error saving Person  ()
[810306] Error running 'SetValues' in logic module 'VI.DB.Entities.EntityScriptLogic'.
[810126] Identities: Error applying dynamic template for Preferred name on object  ().
[810222] Error running script 'Tmpl_Person_PreferredName'.
[System.ArgumentOutOfRangeException] Index and length must refer to a location within the string.
Parameter name: length
2024-01-18 10:01:49 -03:00 - Serious -     Line data was: "125942";"Jessica";"Matias De Jesus";"2";"";"Jessica  Matias De Jesus";"Estagiario Sem Bolsa";"2";"1996-07-11 00:00:00";"0";"50009242";"I5781286";"2023-12-09 00:00:00";"9999-12-31 00:00:00";"Impar Serv.Hospitalares";"69259";"0291";"BR";"";"61998281776";"<EMAIL>";"Hospital Águas Clara";"056.100.891-40";"3318427";"Estagiários";"Estagiário superior";"Mercado Hospit DASA";"Brasília";"2023-12-04 20:36:37";"1"
2024-01-18 10:01:56 -03:00 - Serious -     Error in line 11131: [810457] Error saving Person  ()
[810306] Error running 'SetValues' in logic module 'VI.DB.Entities.EntityScriptLogic'.
[810126] Identities: Error applying dynamic template for Preferred name on object  ().
[810222] Error running script 'Tmpl_Person_PreferredName'.
[System.ArgumentOutOfRangeException] Index and length must refer to a location within the string.
Parameter name: length
2024-01-18 10:01:56 -03:00 - Serious -     Line data was: "126999";"Maria";"Schneiders";"2";"";"Maria  Schneiders";"Estagiário superior";"2";"2000-11-16 00:00:00";"0";"50008895";"P01156G415";"2024-01-15 00:00:00";"9999-12-31 00:00:00";"Lab.Exame Anal.Clinica Lt";"88658";"0320";"BR";"";"51996240751";"<EMAIL>";"Grupo Exame";"040.549.680-07";"1131812974";"Estagiários";"Estagiário superior";"NTOs e Especialidade";"Novo Hamburgo";"2024-01-17 08:56:19";"1"
2024-01-18 10:01:59 -03:00 - Serious -     Error in line 11347: [810457] Error saving Person  ()
[810306] Error running 'SetValues' in logic module 'VI.DB.Entities.EntityScriptLogic'.
[810126] Identities: Error applying dynamic template for Preferred name on object  ().
[810222] Error running script 'Tmpl_Person_PreferredName'.
[System.ArgumentOutOfRangeException] Index and length must refer to a location within the string.
Parameter name: length
2024-01-18 10:01:59 -03:00 - Serious -     Line data was: "125947";"Laiza";"Oliveira Melo Rodrigues";"2";"";"Laiza  Oliveira Melo Rodrigues";"Estagiario Sem Bolsa";"2";"1997-05-09 00:00:00";"0";"50009242";"I5781286";"2023-12-09 00:00:00";"9999-12-31 00:00:00";"Impar Serv.Hospitalares";"69259";"0291";"BR";"";"61981046174";"<EMAIL>";"Hospital Águas Clara";"057.771.101-69";"3361369";"Estagiários";"Estagiário superior";"Mercado Hospit DASA";"Brasília";"2023-12-04 20:36:39";"1"
2024-01-18 10:02:14 -03:00 - Serious -     Error in line 11972: [810457] Error saving Person  ()
[810306] Error running 'SetValues' in logic module 'VI.DB.Entities.EntityScriptLogic'.
[810126] Identities: Error applying dynamic template for Preferred name on object  ().
[810222] Error running script 'Tmpl_Person_PreferredName'.
[System.ArgumentOutOfRangeException] Index and length must refer to a location within the string.
Parameter name: length
2024-01-18 10:02:14 -03:00 - Serious -     Line data was: "126977";"Marcus";"Vinicius Lima Dos Santos";"1";"";"Marcus  Vinicius Lima Dos Santos";"Estagiario Sem Bolsa";"1";"1991-05-17 00:00:00";"0";"50009190";"I5781018";"2024-01-22 00:00:00";"9999-12-31 00:00:00";"Impar Serv.Hospitalares";"118128";"0291";"BR";"";"61985701710";"<EMAIL>";"Hospital Águas Clara";"037.749.421-61";"2759240";"Estagiários";"Estagiário superior";"Mercado Hospit DASA";"Brasília";"2024-01-12 22:38:21";"1"
2024-01-18 10:03:23 -03:00 - Serious -     Error in line 13232: [810457] Error saving Person Maria das Dores dos Santos (F06968636778)
[810306] Error running 'SetValues' in logic module 'VI.DB.Entities.EntityScriptLogic'.
[810126] Identities: Error applying dynamic template for Preferred name on object Maria das Dores dos Santos (F06968636778).
[810222] Error running script 'Tmpl_Person_PreferredName'.
[System.ArgumentOutOfRangeException] Index and length must refer to a location within the string.
Parameter name: length
2024-01-18 10:03:23 -03:00 - Serious -     Line data was: "96100";"Maria";"das Dores dos Santos  Leonardelli";"2";"";"Maria das Dores dos Santos  Leonardelli";"Auxiliar Serviços Gerais";"2";"1972-09-16 00:00:00";"0";"50007460";"I8023221";"2022-04-04 00:00:00";"9999-12-31 00:00:00";"Casa Saude N.Sra.Carmo Lt";"123376";"0331";"BR";"";"(21) 982015185";"<EMAIL>";"Hospital do Carmo";"069.686.367-78";"16016672";"Op Administrativos";"Assistente";"Mercado Hospit DASA";"Rio de Janeiro";"2024-01-17 08:56:20";"1"
2024-01-18 10:03:34 -03:00 - Serious -     Error in line 13491: [810457] Error saving Person  ()
[810306] Error running 'SetValues' in logic module 'VI.DB.Entities.EntityScriptLogic'.
[810126] Identities: Error applying dynamic template for Preferred name on object  ().
[810222] Error running script 'Tmpl_Person_PreferredName'.
[System.ArgumentOutOfRangeException] Index and length must refer to a location within the string.
Parameter name: length
2024-01-18 10:03:34 -03:00 - Serious -     Line data was: "127256";"Maria";"Alice Guarini Rocha";"2";"";"Maria  Alice Guarini Rocha";"Estagiario Sem Bolsa";"2";"2001-02-22 00:00:00";"0";"50009776";"P01311G531";"2024-01-15 00:00:00";"9999-12-31 00:00:00";"Patologia C.Dr.G.Lustosa";"114826";"0398";"BR";"";"32984912867";"<EMAIL>";"LUSTOSA";"082.074.626-63";"MG21043757";"Estagiários";"Estagiário superior";"Privado MG";"Belo Horizonte";"2024-01-12 22:38:21";"1"
2024-01-18 10:03:40 -03:00 - Serious -     Error in line 13836: [810457] Error saving Person  ()
[810306] Error running 'SetValues' in logic module 'VI.DB.Entities.EntityScriptLogic'.
[810126] Identities: Error applying dynamic template for Preferred name on object  ().
[810222] Error running script 'Tmpl_Person_PreferredName'.
[System.ArgumentOutOfRangeException] Index and length must refer to a location within the string.
Parameter name: length
2024-01-18 10:03:40 -03:00 - Serious -     Line data was: "125877";"Brenda";"Galdino Jardim";"2";"";"Brenda  Galdino Jardim";"Estagiario Sem Bolsa";"2";"1999-07-21 00:00:00";"0";"50009776";"P01311G531";"2023-12-04 00:00:00";"9999-12-31 00:00:00";"Patologia C.Dr.G.Lustosa";"114826";"0398";"BR";"";"31992487596";"<EMAIL>";"LUSTOSA";"099.460.346-03";"MG16396289";"Estagiários";"Estagiário superior";"Privado MG";"Belo Horizonte";"2023-11-30 20:50:07";"1"
2024-01-18 10:03:57 -03:00 - Serious -     Error in line 14244: [810457] Error saving Person  ()
[810306] Error running 'SetValues' in logic module 'VI.DB.Entities.EntityScriptLogic'.
[810126] Identities: Error applying dynamic template for Preferred name on object  ().
[810222] Error running script 'Tmpl_Person_PreferredName'.
[System.ArgumentOutOfRangeException] Index and length must refer to a location within the string.
Parameter name: length
2024-01-18 10:03:57 -03:00 - Serious -     Line data was: "125893";"Soraia";"Aguiar De Sousa";"2";"";"Soraia  Aguiar De Sousa";"Estagiario Sem Bolsa";"2";"2002-03-11 00:00:00";"0";"50009293";"I5781422";"2023-12-04 00:00:00";"9999-12-31 00:00:00";"Impar Serv.Hospitalares";"69182";"0291";"BR";"";"61984035188";"<EMAIL>";"Hospital Águas Clara";"068.040.291-86";"3598712";"Estagiários";"Estagiário superior";"Mercado Hospit DASA";"Brasília";"2023-11-30 20:50:10";"1"
2024-01-18 10:04:01 -03:00 - Serious -     Error in line 14302: [810457] Error saving Person  ()
[810306] Error running 'SetValues' in logic module 'VI.DB.Entities.EntityScriptLogic'.
[810126] Identities: Error applying dynamic template for Preferred name on object  ().
[810222] Error running script 'Tmpl_Person_PreferredName'.
[System.ArgumentOutOfRangeException] Index and length must refer to a location within the string.
Parameter name: length
2024-01-18 10:04:01 -03:00 - Serious -     Line data was: "125813";"Daniela";"Martins Dos Passos";"2";"";"Daniela  Martins Dos Passos";"Estagiario Sem Bolsa";"2";"1993-02-05 00:00:00";"0";"50009206";"I5781457";"2023-12-04 00:00:00";"9999-12-31 00:00:00";"Impar Serv.Hospitalares";"69425";"0291";"BR";"";"61996552639";"<EMAIL>";"Hospital Águas Clara";"042.823.301-55";"2772892";"Estagiários";"Estagiário superior";"Mercado Hospit DASA";"Brasília";"2023-11-29 20:43:05";"1"
2024-01-18 10:05:49 -03:00 - Serious -     Error in line 17506: [810391] Error applying changes to database.
[810076] Identities: Value in field Personnel number must be unique. Object Maria Cleuza Soares da Silva (F01767299923) already has this value (359).
2024-01-18 10:05:49 -03:00 - Serious -     Line data was: "359";"Fernando";"Soares da Silva";"1";"";"Fernando Soares da Silva";"Assistente Coleta II";"1";"1980-09-01 00:00:00";"2";"50008744";"U01011D258";"1995-08-07 00:00:00";"9999-12-31 00:00:00";"Diagnósticos da AméricaSA";"2209";"0062";"BR";"";"";"<EMAIL>";"Frischmann";"029.182.459-50";"71244725";"Op Técnicos";"Assistente Técnico";"Regional Sul";"São José dos Pi";"2023-11-16 20:24:24";"1"
2024-01-18 10:05:51 -03:00 - Serious -     Error in line 17570: [810457] Error saving Person  ()
[810306] Error running 'SetValues' in logic module 'VI.DB.Entities.EntityScriptLogic'.
[810126] Identities: Error applying dynamic template for Preferred name on object  ().
[810222] Error running script 'Tmpl_Person_PreferredName'.
[System.ArgumentOutOfRangeException] Index and length must refer to a location within the string.
Parameter name: length
2024-01-18 10:05:51 -03:00 - Serious -     Line data was: "125844";"Maria";"Carlas Coelho De Almeida Brito";"2";"";"Maria  Carlas Coelho De Almeida Brito";"Estagiario Sem Bolsa";"2";"1980-08-28 00:00:00";"0";"50009190";"I5781018";"2023-12-04 00:00:00";"9999-12-31 00:00:00";"Impar Serv.Hospitalares";"118128";"0291";"BR";"";"61984770271";"<EMAIL>";"Hospital Águas Clara";"055.402.216-80";"4133370";"Estagiários";"Estagiário superior";"Mercado Hospit DASA";"Brasília";"2023-11-29 20:43:08";"1"
2024-01-18 10:05:52 -03:00 - Serious -     Error in line 17676: [810457] Error saving Person  ()
[810306] Error running 'SetValues' in logic module 'VI.DB.Entities.EntityScriptLogic'.
[810126] Identities: Error applying dynamic template for Preferred name on object  ().
[810222] Error running script 'Tmpl_Person_PreferredName'.
[System.ArgumentOutOfRangeException] Index and length must refer to a location within the string.
Parameter name: length
2024-01-18 10:05:52 -03:00 - Serious -     Line data was: "126986";"Kessia";"Nayara Castro Dos Santos";"2";"";"Kessia  Nayara Castro Dos Santos";"Estagiario Sem Bolsa";"2";"1995-03-28 00:00:00";"0";"50009242";"I5781286";"2024-01-15 00:00:00";"9999-12-31 00:00:00";"Impar Serv.Hospitalares";"69259";"0291";"BR";"";"61985337630";"<EMAIL>";"Hospital Águas Clara";"057.297.131-18";"3410051";"Estagiários";"Estagiário superior";"Mercado Hospit DASA";"Brasília";"2024-01-09 20:50:30";"1"
2024-01-18 10:05:54 -03:00 - Serious -     Error in line 17870: [810457] Error saving Person  ()
[810306] Error running 'SetValues' in logic module 'VI.DB.Entities.EntityScriptLogic'.
[810126] Identities: Error applying dynamic template for Preferred name on object  ().
[810222] Error running script 'Tmpl_Person_PreferredName'.
[System.ArgumentOutOfRangeException] Index and length must refer to a location within the string.
Parameter name: length
2024-01-18 10:05:54 -03:00 - Serious -     Line data was: "127151";"Izabel";"Cerqueira Pinto";"2";"";"Izabel  Cerqueira Pinto";"Estagiario Sem Bolsa";"2";"2000-10-18 00:00:00";"0";"50009776";"P01311G531";"2024-01-15 00:00:00";"9999-12-31 00:00:00";"Patologia C.Dr.G.Lustosa";"114826";"0398";"BR";"";"33988240292";"<EMAIL>";"LUSTOSA";"111.158.616-03";"MG16520200";"Estagiários";"Estagiário superior";"Privado MG";"Belo Horizonte";"2024-01-12 22:38:11";"1"
2024-01-18 10:06:01 -03:00 - Serious -     Error in line 18348: [810457] Error saving Person  ()
[810306] Error running 'SetValues' in logic module 'VI.DB.Entities.EntityScriptLogic'.
[810126] Identities: Error applying dynamic template for Preferred name on object  ().
[810222] Error running script 'Tmpl_Person_PreferredName'.
[System.ArgumentOutOfRangeException] Index and length must refer to a location within the string.
Parameter name: length
2024-01-18 10:06:01 -03:00 - Serious -     Line data was: "126307";"Zenaide";"Soares Dos Santos";"2";"";"Zenaide   Soares Dos Santos";"Estagiario Sem Bolsa";"2";"1983-05-24 00:00:00";"0";"50009293";"I5781422";"2023-12-18 00:00:00";"9999-12-31 00:00:00";"Impar Serv.Hospitalares";"69182";"0291";"BR";"";"61999184494";"<EMAIL>";"Hospital Águas Clara";"058.980.606-85";"2245526";"Estagiários";"Estagiário superior";"Mercado Hospit DASA";"Brasília";"2023-12-14 20:46:36";"1"
2024-01-18 10:06:05 -03:00 - Serious -     Error in line 18637: [810457] Error saving Person  ()
[810306] Error running 'SetValues' in logic module 'VI.DB.Entities.EntityScriptLogic'.
[810126] Identities: Error applying dynamic template for Preferred name on object  ().
[810222] Error running script 'Tmpl_Person_PreferredName'.
[System.ArgumentOutOfRangeException] Index and length must refer to a location within the string.
Parameter name: length
2024-01-18 10:06:05 -03:00 - Serious -     Line data was: "125955";"Samara";"Kathleen Silva Sena";"2";"";"Samara  Kathleen Silva Sena";"Estagiario Sem Bolsa";"2";"2002-07-14 00:00:00";"0";"50009293";"I5781422";"2023-12-09 00:00:00";"9999-12-31 00:00:00";"Impar Serv.Hospitalares";"69182";"0291";"BR";"";"61999836179";"<EMAIL>";"Hospital Águas Clara";"063.083.371-02";"3551646";"Estagiários";"Estagiário superior";"Mercado Hospit DASA";"Brasília";"2023-12-05 20:14:01";"1"
2024-01-18 10:06:25 -03:00 - Serious -     Error in line 19693: [810457] Error saving Person Larissa Cristina Favetta Stephan (F45827580880)
[810306] Error running 'SetValues' in logic module 'VI.DB.Entities.EntityScriptLogic'.
[810126] Identities: Error applying dynamic template for Preferred name on object Larissa Cristina Favetta Stephan (F45827580880).
[810222] Error running script 'Tmpl_Person_PreferredName'.
[System.ArgumentOutOfRangeException] Index and length must refer to a location within the string.
Parameter name: length
2024-01-18 10:06:25 -03:00 - Serious -     Line data was: "100547";"Larissa";"Cristina Favetta Stephan";"2";"";"Larissa Cristina Favetta  Stephan";"Analista Laboratorio I";"2";"1996-03-06 00:00:00";"0";"50007584";"P01105P022";"2022-09-12 00:00:00";"9999-12-31 00:00:00";"Previlab Análises Clínica";"52220";"0047";"BR";"";"(19)998165332";"<EMAIL>";"Previlab";"458.275.808-80";"405982732";"Op Técnicos";"Analista Técnico";"Regional SP";"Piracicaba";"2024-01-16 08:27:35";"1"
2024-01-18 10:06:28 -03:00 - Serious -     Error in line 19926: [810457] Error saving Person  ()
[810306] Error running 'SetValues' in logic module 'VI.DB.Entities.EntityScriptLogic'.
[810126] Identities: Error applying dynamic template for Preferred name on object  ().
[810222] Error running script 'Tmpl_Person_PreferredName'.
[System.ArgumentOutOfRangeException] Index and length must refer to a location within the string.
Parameter name: length
2024-01-18 10:06:28 -03:00 - Serious -     Line data was: "125949";"Larissa";"Ester de Jesus Ramos";"2";"";"Larissa  Ester de Jesus Ramos";"Estagiario Sem Bolsa";"2";"1993-09-23 00:00:00";"0";"50009190";"I5781018";"2023-12-09 00:00:00";"9999-12-31 00:00:00";"Impar Serv.Hospitalares";"118128";"0291";"BR";"";"38999444800";"<EMAIL>";"Hospital Águas Clara";"121.948.516-07";"MG18676777";"Estagiários";"Estagiário superior";"Mercado Hospit DASA";"Brasília";"2023-12-08 20:48:39";"1"
2024-01-18 10:06:56 -03:00 - Serious -     Error in line 21413: [810457] Error saving Person  ()
[810306] Error running 'SetValues' in logic module 'VI.DB.Entities.EntityScriptLogic'.
[810126] Identities: Error applying dynamic template for Preferred name on object  ().
[810222] Error running script 'Tmpl_Person_PreferredName'.
[System.ArgumentOutOfRangeException] Index and length must refer to a location within the string.
Parameter name: length
2024-01-18 10:06:56 -03:00 - Serious -     Line data was: "126104";"Marilda";"Fernandes De Carvalho";"2";"";"Marilda  Fernandes De Carvalho";"Estagiario Sem Bolsa";"2";"1983-10-11 00:00:00";"0";"50009190";"I5781018";"2023-12-11 00:00:00";"9999-12-31 00:00:00";"Impar Serv.Hospitalares";"118128";"0291";"BR";"";"61993797676";"<EMAIL>";"Hospital Águas Clara";"726.949.041-49";"1200247566";"Estagiários";"Estagiário superior";"Mercado Hospit DASA";"Brasília";"2023-12-07 20:19:55";"1"
2024-01-18 10:08:11 -03:00 - Serious -     Error in line 24238: [810457] Error saving Person  ()
[810306] Error running 'SetValues' in logic module 'VI.DB.Entities.EntityScriptLogic'.
[810126] Identities: Error applying dynamic template for Preferred name on object  ().
[810222] Error running script 'Tmpl_Person_PreferredName'.
[System.ArgumentOutOfRangeException] Index and length must refer to a location within the string.
Parameter name: length
2024-01-18 10:08:11 -03:00 - Serious -     Line data was: "125956";"Jaqueline";"Pereira de Sousa";"2";"";"Jaqueline  Pereira de Sousa";"Estagiario Sem Bolsa";"2";"1997-10-29 00:00:00";"0";"50009242";"I5781286";"2023-12-09 00:00:00";"9999-12-31 00:00:00";"Impar Serv.Hospitalares";"69259";"0291";"BR";"";"(61) 9648-4945";"<EMAIL>";"Hospital Águas Clara";"140.554.176-83";"4121294";"Estagiários";"Estagiário superior";"Mercado Hospit DASA";"Brasília";"2023-12-05 20:13:55";"1"
2024-01-18 10:08:17 -03:00 - Serious -     Error in line 24493: [810457] Error saving Person  ()
[810306] Error running 'SetValues' in logic module 'VI.DB.Entities.EntityScriptLogic'.
[810126] Identities: Error applying dynamic template for Preferred name on object  ().
[810222] Error running script 'Tmpl_Person_PreferredName'.
[System.ArgumentOutOfRangeException] Index and length must refer to a location within the string.
Parameter name: length
2024-01-18 10:08:17 -03:00 - Serious -     Line data was: "125946";"Juliana";"Silva Jossi Ferreira";"2";"";"Juliana  Silva Jossi Ferreira";"Estagiario Sem Bolsa";"2";"1995-03-10 00:00:00";"0";"50009242";"I5781286";"2023-12-09 00:00:00";"9999-12-31 00:00:00";"Impar Serv.Hospitalares";"69259";"0291";"BR";"";"";"";"Hospital Águas Clara";"354.948.658-86";"7414693";"Estagiários";"Estagiário superior";"Mercado Hospit DASA";"Brasília";"2023-12-04 20:36:38";"1"
2024-01-18 10:09:25 -03:00 - Serious -     Error in line 28006: [810457] Error saving Person Etienne de Oliveira Ventura (F13991936780)
[810306] Error running 'SetValues' in logic module 'VI.DB.Entities.EntityScriptLogic'.
[810126] Identities: Error applying dynamic template for Preferred name on object Etienne de Oliveira Ventura (F13991936780).
[810222] Error running script 'Tmpl_Person_PreferredName'.
[System.ArgumentOutOfRangeException] Index and length must refer to a location within the string.
Parameter name: length
2024-01-18 10:09:25 -03:00 - Serious -     Line data was: "32020";"Etienne";"Ventura  Duarte";"2";"";"Etienne Ventura  Duarte";"Recepcionista Líder";"2";"1990-06-07 00:00:00";"2";"50008929";"U01012DB05";"2017-12-18 00:00:00";"9999-12-31 00:00:00";"Diagnósticos da AméricaSA";"8006";"0053";"BR";"";"21 996017333";"<EMAIL>";"CDPI";"139.919.367-80";"254836729";"Op Técnicos";"Lider Técnico";"Regional RJ";"Rio de Janeiro";"2024-01-17 08:56:01";"1"
2024-01-18 10:10:34 -03:00 - Serious -     Error in line 30395: [810457] Error saving Person  ()
[810306] Error running 'SetValues' in logic module 'VI.DB.Entities.EntityScriptLogic'.
[810126] Identities: Error applying dynamic template for Preferred name on object  ().
[810222] Error running script 'Tmpl_Person_PreferredName'.
[System.ArgumentOutOfRangeException] Index and length must refer to a location within the string.
Parameter name: length
2024-01-18 10:10:34 -03:00 - Serious -     Line data was: "125795";"Rosangela";"Cristina De Souza";"2";"";"Rosangela  Cristina De Souza";"Estagiario Sem Bolsa";"2";"1978-08-20 00:00:00";"0";"50009293";"I5781422";"2023-12-04 00:00:00";"9999-12-31 00:00:00";"Impar Serv.Hospitalares";"69182";"0291";"BR";"";"(61) 9326-1247";"<EMAIL>";"Hospital Águas Clara";"281.075.618-05";"36291008";"Estagiários";"Estagiário superior";"Mercado Hospit DASA";"Brasília";"2023-11-29 20:43:09";"1"
2024-01-18 10:11:37 -03:00 - Serious -     Error in line 32955: [810457] Error saving Person  ()
[810306] Error running 'SetValues' in logic module 'VI.DB.Entities.EntityScriptLogic'.
[810126] Identities: Error applying dynamic template for Preferred name on object  ().
[810222] Error running script 'Tmpl_Person_PreferredName'.
[System.ArgumentOutOfRangeException] Index and length must refer to a location within the string.
Parameter name: length
2024-01-18 10:11:37 -03:00 - Serious -     Line data was: "126297";"Thaynara";"Soares Silva Cruz";"2";"";"Thaynara  Soares Silva Cruz";"Estagiario Sem Bolsa";"2";"1997-01-09 00:00:00";"0";"50009293";"I5781422";"2023-12-18 00:00:00";"9999-12-31 00:00:00";"Impar Serv.Hospitalares";"69182";"0291";"BR";"";"61995942427";"<EMAIL>";"Hospital Águas Clara";"055.084.581-07";"3285930";"Estagiários";"Estagiário superior";"Mercado Hospit DASA";"Brasília";"2023-12-14 20:46:36";"1"
2024-01-18 10:11:53 -03:00 - Serious -     Error in line 33975: [810457] Error saving Person Gilvaneide Rodrigues Ferreira Moura (F33107239844)
[810306] Error running 'SetValues' in logic module 'VI.DB.Entities.EntityScriptLogic'.
[810126] Identities: Error applying dynamic template for Preferred name on object Gilvaneide Rodrigues Ferreira Moura (F33107239844).
[810222] Error running script 'Tmpl_Person_PreferredName'.
[System.ArgumentOutOfRangeException] Index and length must refer to a location within the string.
Parameter name: length
2024-01-18 10:11:53 -03:00 - Serious -     Line data was: "38971";"Gilvaneide";"Rodrigues Ferreira Santos";"2";"";"Gilvaneide  Rodrigues Ferreira Santos";"Recepcionista I";"2";"1984-04-10 00:00:00";"0";"50008942";"U01012SZ04";"2015-10-05 00:00:00";"9999-12-31 00:00:00";"Salomão e Zoppi Serviços";"38643";"0160";"BR";"";"(11) 974515023";"<EMAIL>";"Salomão Zoppi";"331.072.398-44";"346815617";"Op Técnicos";"Assistente Técnico";"NTOs e Especialidade";"São Paulo";"2023-12-12 08:22:53";"1"
2024-01-18 10:12:14 -03:00 - Serious -     Error in line 35257: [810457] Error saving Person  ()
[810306] Error running 'SetValues' in logic module 'VI.DB.Entities.EntityScriptLogic'.
[810126] Identities: Error applying dynamic template for Preferred name on object  ().
[810222] Error running script 'Tmpl_Person_PreferredName'.
[System.ArgumentOutOfRangeException] Index and length must refer to a location within the string.
Parameter name: length
2024-01-18 10:12:14 -03:00 - Serious -     Line data was: "125952";"Aline";"Costa Fernandes";"2";"";"Aline  Costa Fernandes";"Estagiario Sem Bolsa";"2";"1999-10-15 00:00:00";"0";"50009206";"I5781457";"2023-12-09 00:00:00";"9999-12-31 00:00:00";"Impar Serv.Hospitalares";"69425";"0291";"BR";"";"61983326539";"<EMAIL>";"Hospital Águas Clara";"608.641.453-06";"4486077";"Estagiários";"Estagiário superior";"Mercado Hospit DASA";"Brasília";"2023-12-05 20:13:47";"1"
2024-01-18 10:12:20 -03:00 - Serious -     Error in line 35686: [810457] Error saving Person  ()
[810306] Error running 'SetValues' in logic module 'VI.DB.Entities.EntityScriptLogic'.
[810126] Identities: Error applying dynamic template for Preferred name on object  ().
[810222] Error running script 'Tmpl_Person_PreferredName'.
[System.ArgumentOutOfRangeException] Index and length must refer to a location within the string.
Parameter name: length
2024-01-18 10:12:20 -03:00 - Serious -     Line data was: "126970";"Ismirna";"Monteiro Da Silva";"2";"";"Ismirna  Monteiro Da Silva";"Estagiario Sem Bolsa";"2";"2000-12-16 00:00:00";"0";"50009242";"I5781286";"2024-01-15 00:00:00";"9999-12-31 00:00:00";"Impar Serv.Hospitalares";"69259";"0291";"BR";"";"61982820571";"<EMAIL>";"Hospital Águas Clara";"626.922.743-77";"0621578320177";"Estagiários";"Estagiário superior";"Mercado Hospit DASA";"Brasília";"2024-01-09 20:50:28";"1"
2024-01-18 10:12:22 -03:00 - Serious -     Error in line 35823: [810457] Error saving Person  ()
[810306] Error running 'SetValues' in logic module 'VI.DB.Entities.EntityScriptLogic'.
[810126] Identities: Error applying dynamic template for Preferred name on object  ().
[810222] Error running script 'Tmpl_Person_PreferredName'.
[System.ArgumentOutOfRangeException] Index and length must refer to a location within the string.
Parameter name: length
2024-01-18 10:12:22 -03:00 - Serious -     Line data was: "125931";"Adriana";"dos Santos";"2";"";"Adriana  dos Santos";"Estagiario Sem Bolsa";"2";"1981-10-30 00:00:00";"0";"50009206";"I5781457";"2023-12-09 00:00:00";"9999-12-31 00:00:00";"Impar Serv.Hospitalares";"69425";"0291";"BR";"";"61993608985";"<EMAIL>";"Hospital Águas Clara";"720.524.571-00";"1883354";"Estagiários";"Estagiário superior";"Mercado Hospit DASA";"Brasília";"2023-12-04 20:36:27";"1"
2024-01-18 10:12:36 -03:00 - Serious -     Error in line 36655: [810457] Error saving Person  ()
[810306] Error running 'SetValues' in logic module 'VI.DB.Entities.EntityScriptLogic'.
[810126] Identities: Error applying dynamic template for Preferred name on object  ().
[810222] Error running script 'Tmpl_Person_PreferredName'.
[System.ArgumentOutOfRangeException] Index and length must refer to a location within the string.
Parameter name: length
2024-01-18 10:12:36 -03:00 - Serious -     Line data was: "126100";"Isabella";"Christine Vilhena Dos Santos";"2";"";"Isabella  Christine Vilhena Dos Santos";"Estagiario Sem Bolsa";"2";"2004-05-14 00:00:00";"0";"50009242";"I5781286";"2023-12-11 00:00:00";"9999-12-31 00:00:00";"Impar Serv.Hospitalares";"69259";"0291";"BR";"";"61986513209";"<EMAIL>";"Hospital Águas Clara";"072.100.091-67";"3706206";"Estagiários";"Estagiário superior";"Mercado Hospit DASA";"Brasília";"2023-12-07 20:19:53";"1"
2024-01-18 10:13:35 -03:00 - Serious -     Error in line 39661: [810457] Error saving Person  ()
[810306] Error running 'SetValues' in logic module 'VI.DB.Entities.EntityScriptLogic'.
[810126] Identities: Error applying dynamic template for Preferred name on object  ().
[810222] Error running script 'Tmpl_Person_PreferredName'.
[System.ArgumentOutOfRangeException] Index and length must refer to a location within the string.
Parameter name: length
2024-01-18 10:13:35 -03:00 - Serious -     Line data was: "126974";"Ludmilla";"de Oliveira Acosta Martins";"2";"";"Ludmilla  de Oliveira Acosta Martins";"Estagiario Sem Bolsa";"2";"1988-07-16 00:00:00";"0";"50009242";"I5781286";"2024-01-15 00:00:00";"9999-12-31 00:00:00";"Impar Serv.Hospitalares";"69259";"0291";"BR";"";"61984194798";"<EMAIL>";"Hospital Águas Clara";"704.138.601-59";"2542312";"Estagiários";"Estagiário superior";"Mercado Hospit DASA";"Brasília";"2024-01-09 20:50:31";"1"
2024-01-18 10:14:50 -03:00 - Serious -     Error in line 43387: [810391] Error applying changes to database.
[810076] Identities: Value in field Personnel number must be unique. Object Ivonete Mendes de Carvalho Ribeiro (F31214230881) already has this value (30194).
2024-01-18 10:14:50 -03:00 - Serious -     Line data was: "30194";"Ivonete";"Mendes de Carvalho Ribeiro";"2";"";"Ivonete Mendes de Carvalho Ribeiro";"Tecnico Laboratorio I";"2";"1988-03-22 00:00:00";"5";"50008855";"P01031D474";"2017-07-17 00:00:00";"9999-12-31 00:00:00";"Diagnósticos da AméricaSA";"47015";"0005";"BR";"";"11 95761-5347";"<EMAIL>";"Dasa";"364.665.268-36";"453732732";"Op Técnicos";"Técnico";"NTOs e Especialidade";"Barueri";"2023-12-18 21:08:57";"1"
2024-01-18 10:15:09 -03:00 - Serious -     Error in line 44406: [810457] Error saving Person  ()
[810306] Error running 'SetValues' in logic module 'VI.DB.Entities.EntityScriptLogic'.
[810126] Identities: Error applying dynamic template for Preferred name on object  ().
[810222] Error running script 'Tmpl_Person_PreferredName'.
[System.ArgumentOutOfRangeException] Index and length must refer to a location within the string.
Parameter name: length
2024-01-18 10:15:09 -03:00 - Serious -     Line data was: "127010";"Vilma";"Santos Moreira";"2";"";"Vilma  Santos Moreira";"Estagiario Sem Bolsa";"2";"1978-11-18 00:00:00";"0";"50009242";"I5781286";"2024-01-15 00:00:00";"9999-12-31 00:00:00";"Impar Serv.Hospitalares";"69259";"0291";"BR";"";"61982368772";"<EMAIL>";"Hospital Águas Clara";"261.660.568-69";"3964924";"Estagiários";"Estagiário superior";"Mercado Hospit DASA";"Brasília";"2024-01-09 20:50:36";"1"
2024-01-18 10:15:15 -03:00 - Serious -     Error in line 44832: [810391] Error applying changes to database.
[810076] Identities: Value in field Personnel number must be unique. Object Alcides Ferreira Honorato (F64425932820) already has this value (264).
2024-01-18 10:15:15 -03:00 - Serious -     Line data was: "264";"Erenildes";"Rodrigues Honorato";"2";"";"Erenildes Rodrigues Honorato";"Assistente Coleta II";"2";"1953-04-13 00:00:00";"2";"50008744";"U01011D091";"1994-05-16 00:00:00";"9999-12-31 00:00:00";"Diagnósticos da AméricaSA";"2420";"0065";"BR";"";"";"<EMAIL>";"Delboni Auriemo";"292.005.618-29";"8461596";"Op Técnicos";"Assistente Técnico";"Regional SP";"São Paulo";"2023-09-17 15:40:38";"1"
2024-01-18 10:15:55 -03:00 - Serious -     Error in line 46468: [810457] Error saving Person  ()
[810306] Error running 'SetValues' in logic module 'VI.DB.Entities.EntityScriptLogic'.
[810126] Identities: Error applying dynamic template for Preferred name on object  ().
[810222] Error running script 'Tmpl_Person_PreferredName'.
[System.ArgumentOutOfRangeException] Index and length must refer to a location within the string.
Parameter name: length
2024-01-18 10:15:55 -03:00 - Serious -     Line data was: "126981";"Maria";"Luana Gomes Da Cunha";"2";"";"Maria  Luana Gomes Da Cunha";"Estagiario Sem Bolsa";"2";"1999-11-14 00:00:00";"0";"50009242";"I5781286";"2024-01-15 00:00:00";"9999-12-31 00:00:00";"Impar Serv.Hospitalares";"69259";"0291";"BR";"";"61994444398";"<EMAIL>";"Hospital Águas Clara";"620.923.893-94";"0552129020152";"Estagiários";"Estagiário superior";"Mercado Hospit DASA";"Brasília";"2024-01-09 20:50:32";"1"
2024-01-18 10:15:55 -03:00 - Serious -     Error in line 46499: [810457] Error saving Person  ()
[810306] Error running 'SetValues' in logic module 'VI.DB.Entities.EntityScriptLogic'.
[810126] Identities: Error applying dynamic template for Preferred name on object  ().
[810222] Error running script 'Tmpl_Person_PreferredName'.
[System.ArgumentOutOfRangeException] Index and length must refer to a location within the string.
Parameter name: length
2024-01-18 10:15:55 -03:00 - Serious -     Line data was: "126106";"Marizete";"Alves De Jesus";"2";"";"Marizete  Alves De Jesus";"Estagiario Sem Bolsa";"2";"1974-05-12 00:00:00";"0";"50009190";"I5781018";"2023-12-11 00:00:00";"9999-12-31 00:00:00";"Impar Serv.Hospitalares";"118128";"0291";"BR";"";"61984178380";"<EMAIL>";"Hospital Águas Clara";"646.074.021-68";"1415360";"Estagiários";"Estagiário superior";"Mercado Hospit DASA";"Brasília";"2023-12-07 20:19:55";"1"
2024-01-18 10:16:15 -03:00 - Serious -     Error in line 47517: [810457] Error saving Person  ()
[810306] Error running 'SetValues' in logic module 'VI.DB.Entities.EntityScriptLogic'.
[810126] Identities: Error applying dynamic template for Preferred name on object  ().
[810222] Error running script 'Tmpl_Person_PreferredName'.
[System.ArgumentOutOfRangeException] Index and length must refer to a location within the string.
Parameter name: length
2024-01-18 10:16:15 -03:00 - Serious -     Line data was: "126315";"Rozenilde";"Rodrigues Da Silva";"2";"";"Rozenilde  Rodrigues Da Silva";"Estagiario Sem Bolsa";"2";"1970-10-20 00:00:00";"0";"50009293";"I5781422";"2023-12-18 00:00:00";"9999-12-31 00:00:00";"Impar Serv.Hospitalares";"69182";"0291";"BR";"";"61992632725";"<EMAIL>";"Hospital Águas Clara";"512.284.081-49";"1191452";"Estagiários";"Estagiário superior";"Mercado Hospit DASA";"Brasília";"2023-12-14 20:46:35";"1"
2024-01-18 10:16:24 -03:00 - Serious -     Error in line 48173: [810457] Error saving Person  ()
[810306] Error running 'SetValues' in logic module 'VI.DB.Entities.EntityScriptLogic'.
[810126] Identities: Error applying dynamic template for Preferred name on object  ().
[810222] Error running script 'Tmpl_Person_PreferredName'.
[System.ArgumentOutOfRangeException] Index and length must refer to a location within the string.
Parameter name: length
2024-01-18 10:16:24 -03:00 - Serious -     Line data was: "126102";"Maria";"Jose Gomes Martins Mendes";"2";"";"Maria  Jose Gomes Martins Mendes";"Estagiario Sem Bolsa";"2";"1975-08-11 00:00:00";"0";"50009190";"I5781018";"2023-12-11 00:00:00";"9999-12-31 00:00:00";"Impar Serv.Hospitalares";"118128";"0291";"BR";"";"61995768448";"<EMAIL>";"Hospital Águas Clara";"887.377.054-15";"9150226";"Estagiários";"Estagiário superior";"Mercado Hospit DASA";"Brasília";"2023-12-07 20:19:55";"1"
2024-01-18 10:16:25 -03:00 - Serious -     Error in line 48291: [810457] Error saving Person  ()
[810306] Error running 'SetValues' in logic module 'VI.DB.Entities.EntityScriptLogic'.
[810126] Identities: Error applying dynamic template for Preferred name on object  ().
[810222] Error running script 'Tmpl_Person_PreferredName'.
[System.ArgumentOutOfRangeException] Index and length must refer to a location within the string.
Parameter name: length
2024-01-18 10:16:25 -03:00 - Serious -     Line data was: "126299";"Alba";"Lucia Da Silva De Freitas";"2";"";"Alba  Lucia Da Silva De Freitas";"Estagiario Sem Bolsa";"2";"1977-10-02 00:00:00";"0";"50009206";"I5781457";"2023-12-18 00:00:00";"9999-12-31 00:00:00";"Impar Serv.Hospitalares";"69425";"0291";"BR";"";"61994098895";"<EMAIL>";"Hospital Águas Clara";"836.458.531-20";"1616562";"Estagiários";"Estagiário superior";"Mercado Hospit DASA";"Brasília";"2023-12-14 20:46:30";"1"
2024-01-18 10:17:01 -03:00 - Info - Results
2024-01-18 10:17:01 -03:00 - Info -     50321 lines imported
2024-01-18 10:17:01 -03:00 - Info -     0 header lines
2024-01-18 10:17:01 -03:00 - Info -     0 inserted
2024-01-18 10:17:01 -03:00 - Info -     36408 changed
2024-01-18 10:17:01 -03:00 - Info -     0 deleted
2024-01-18 10:17:01 -03:00 - Info -     13856 not changed
2024-01-18 10:17:01 -03:00 - Info -     0 not found
2024-01-18 10:17:01 -03:00 - Info -     0 empty lines
2024-01-18 10:17:01 -03:00 - Info -     57 errors
