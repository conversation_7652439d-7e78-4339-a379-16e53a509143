﻿2025-01-23 17:22:37 -03:00 - Info - Indexing existing data...
2025-01-23 17:22:38 -03:00 - Serious -     Duplicate key: 
2025-01-23 17:22:38 -03:00 - Serious -     Duplicate key: 
2025-01-23 17:22:38 -03:00 - Serious -     Duplicate key: 
2025-01-23 17:22:38 -03:00 - Serious -     Duplicate key: 
2025-01-23 17:22:38 -03:00 - Serious -     Duplicate key: 
2025-01-23 17:22:38 -03:00 - Serious -     Duplicate key: 
2025-01-23 17:22:38 -03:00 - Serious -     Duplicate key: 
2025-01-23 17:22:38 -03:00 - Serious -     Duplicate key: 
2025-01-23 17:22:38 -03:00 - Serious -     Duplicate key: 
2025-01-23 17:22:38 -03:00 - Serious -     Duplicate key: 
2025-01-23 17:22:38 -03:00 - Serious -     Duplicate key: 
2025-01-23 17:22:38 -03:00 - Serious -     Duplicate key: 
2025-01-23 17:22:38 -03:00 - Serious -     Duplicate key: 
2025-01-23 17:22:38 -03:00 - Serious -     Duplicate key: 
2025-01-23 17:22:38 -03:00 - Serious -     Duplicate key: 
2025-01-23 17:22:38 -03:00 - Serious -     Duplicate key: 
2025-01-23 17:22:38 -03:00 - Serious -     Duplicate key: 
2025-01-23 17:22:38 -03:00 - Serious -     Duplicate key: 
2025-01-23 17:22:38 -03:00 - Serious -     Duplicate key: 
2025-01-23 17:22:38 -03:00 - Serious -     Duplicate key: 
2025-01-23 17:22:38 -03:00 - Serious -     Duplicate key: 
2025-01-23 17:22:38 -03:00 - Serious -     Duplicate key: 
2025-01-23 17:22:38 -03:00 - Serious -     Duplicate key: 
2025-01-23 17:22:38 -03:00 - Serious -     Duplicate key: 
2025-01-23 17:22:38 -03:00 - Serious -     Duplicate key: 
2025-01-23 17:22:38 -03:00 - Info - Import data from file
2025-01-23 17:22:41 -03:00 - Serious -     Error in line 15518: [810457] Error saving Person Karina Martini de Almeida (T12331523789)
[810306] Error running 'OnGenerate' in logic module 'VI.DB.Entities.EventsEntityLogic'.
[810103] Error generating processes for event Update.
[810222] Error running script 'Event_Update'.
[810108] Error generation process step event UPDATE.
[810109] Error generating process Process SUB-IDENTITY Conta S - Update Info.
[810155] Error running prescript.
[810048] Error getting single property Person.UID_Person.
[810391] Error applying changes to database.
[810076] Identities: Value in field Personnel number must be unique. Object Karina Martini De Almeida (F12331523789) already has this value (98802).
2025-01-23 17:22:41 -03:00 - Serious -     Line data was: "98802";"Karina";"Martini De Almeida";"2";"";"Karina Martini De Almeida";"Assistente Patologia Atendimento";"2";"1987-07-17 00:00:00";"2";"50008744";"U01011D560";"2022-08-01 00:00:00";"9999-12-31 00:00:00";"Diagnósticos da AméricaSA";"2644";"0053";"BR";"";"(21)969341622";"<EMAIL>";"Bronstein";"123.315.237-89";"207876327";"Op Técnicos";"Assistente Técnico";"Regional RJ";"Rio de Janeiro";"2024-11-19 20:15:29";"1"
2025-01-23 17:22:42 -03:00 - Serious -     Error in line 25910: [810457] Error saving Person Gabriel Broad Enedino Fernandes (T18371092733)
[810306] Error running 'OnGenerate' in logic module 'VI.DB.Entities.EventsEntityLogic'.
[810103] Error generating processes for event Update.
[810222] Error running script 'Event_Update'.
[810108] Error generation process step event UPDATE.
[810109] Error generating process Process SUB-IDENTITY Conta S - Update Info.
[810155] Error running prescript.
[810048] Error getting single property Person.UID_Person.
[810391] Error applying changes to database.
[810076] Identities: Value in field Personnel number must be unique. Object Gabriel Broad Florencio Enedino (F18371092733) already has this value (86823).
2025-01-23 17:22:42 -03:00 - Serious -     Line data was: "86823";"Gabriel";"Broad Florencio Enedino";"1";"";"Gabriel Broad Florencio Enedino";"Assistente Coleta Atendimento";"1";"1998-11-11 00:00:00";"0";"50008744";"U01011D198";"2022-02-07 00:00:00";"9999-12-31 00:00:00";"Diagnósticos da AméricaSA";"53694";"0053";"BR";"(21) 2542-1261";"(21)988690585";"<EMAIL>";"Bronstein";"183.710.927-33";"267370344";"Op Técnicos";"Assistente Técnico";"Regional RJ";"Rio de Janeiro";"2024-09-30 20:25:39";"1"
2025-01-23 17:22:43 -03:00 - Serious -     Error in line 43160: [810457] Error saving Person Mariana Costa Cruz (T41007189851)
[810306] Error running 'OnGenerate' in logic module 'VI.DB.Entities.EventsEntityLogic'.
[810103] Error generating processes for event Update.
[810222] Error running script 'Event_Update'.
[810108] Error generation process step event UPDATE.
[810109] Error generating process Process SUB-IDENTITY Conta S - Update Info.
[810155] Error running prescript.
[810048] Error getting single property Person.UID_Person.
[810391] Error applying changes to database.
[810076] Identities: Value in field Personnel number must be unique. Object Mariana Costa Cruz (F41007189851) already has this value (141852).
2025-01-23 17:22:43 -03:00 - Serious -     Line data was: "141852";"Mariana";"Costa Cruz";"2";"";"Mariana Costa Cruz";"Analista Compliance III";"2";"1991-07-29 00:00:00";"0";"50009365";"A09027D896";"2024-10-03 00:00:00";"9999-12-31 00:00:00";"Diagnósticos da AméricaSA";"35879";"0065";"BR";"(14) 9973-6911";"(14)997369111";"<EMAIL>";"Dasa";"410.071.898-51";"47974922X";"Op Administrativos";"Analista";"Jurídico";"São Paulo";"2024-10-02 08:08:59";"1"
2025-01-23 17:22:43 -03:00 - Info - Results
2025-01-23 17:22:43 -03:00 - Info -     49046 lines imported
2025-01-23 17:22:43 -03:00 - Info -     0 header lines
2025-01-23 17:22:43 -03:00 - Info -     0 inserted
2025-01-23 17:22:43 -03:00 - Info -     0 changed
2025-01-23 17:22:43 -03:00 - Info -     0 deleted
2025-01-23 17:22:43 -03:00 - Info -     0 not changed
2025-01-23 17:22:43 -03:00 - Info -     49043 not found
2025-01-23 17:22:43 -03:00 - Info -     0 empty lines
2025-01-23 17:22:43 -03:00 - Info -     3 errors
