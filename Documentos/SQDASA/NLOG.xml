<nlog autoReload="true" xmlns="http://www.nlog-project.org/schemas/NLog.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<variable name="companyName" value="One Identity"/>
	<variable name="productTitle" value="${gdc:QIM_ProductNameShort:whenEmpty=One Identity Manager}"/>
	<variable name="logBaseDir" value="${specialfolder:LocalApplicationData}/${companyName}/${productTitle}/${appName}"/>
	<variable name="logFileLevel" value="Info" />
	<variable name="eventLogLevel" value="Fatal" />

	<!-- Define layouts for log files. Possible values can be found here: https://github.com/NLog/NLog/wiki/Layout-Renderers -->
	<variable name="layout" value="${longdate} ${level:upperCase=true} (${logger} ${event-context:item=SessionId} ${ndlc}) : ${event-context:item=Indention}${message} ${exception:format=ToString,StackTrace}" />
	<variable name="remoteLayout" value="${longdate} ${level:upperCase=true} ${machinename} ${processname} ${windows-identity} (${logger} ${event-context:item=SessionId} ${ndlc}) : ${event-context:item=Indention}${message} ${exception:format=ToString,StackTrace}" />

	<include file="${basedir}/custom-log-variables.config" ignoreErrors="true"/>

	<targets>
		<default-wrapper xsi:type="AsyncWrapper" batchSize="1000" overflowAction="Block" timeToSleepBetweenBatches="0" />

		<target name="logfile" xsi:type="File" fileName="${logBaseDir}/${appName}.log" layout="${layout}" encoding="utf-8"
			archiveFileName="${logBaseDir}/${appName}.{#}.log" maxArchiveFiles="7" archiveEvery="Day" archiveNumbering="Rolling"/>

		<!-- Adapt this entry if you need the more verbose remoteLayout with machine name, process name, and windows identity:
		<target name="logfile" xsi:type="File" fileName="${logBaseDir}/${appName}.log" layout="${remoteLayout}" encoding="utf-8"
			archiveFileName="${logBaseDir}/${appName}.{#}.log" maxArchiveFiles="7" archiveEvery="Day" archiveNumbering="Rolling"/>
		-->

		<!-- Debug logger definitions for debug log	-->
		<target name="debug" xsi:type="File" fileName="${logBaseDir}/debug/${appName}.log" layout="${layout}" encoding="utf-8"
			archiveFileName="${logBaseDir}/debug/${appName}.{#}.log" maxArchiveFiles="7" archiveEvery="Day" archiveNumbering="Rolling"/>
		<target name="sqllogfile" xsi:type="File" fileName="${logBaseDir}/debug/${appName}_sql.log" layout="${layout}" encoding="utf-8"
			archiveFileName="${logBaseDir}/debug/${appName}_sql.{#}.log" maxArchiveFiles="7" archiveEvery="Day" archiveNumbering="Rolling"/>
		<target name="objectlogfile" xsi:type="File" fileName="${logBaseDir}/debug/${appName}_object.log" layout="${layout}" encoding="utf-8"
			archiveFileName="${logBaseDir}/debug/${appName}_object.{#}.log" maxArchiveFiles="7" archiveEvery="Day" archiveNumbering="Rolling"/>
		<target name="jobgenlogfile" xsi:type="File" fileName="${logBaseDir}/debug/${appName}_jobgen.log" layout="${layout}" encoding="utf-8"
			archiveFileName="${logBaseDir}/debug/${appName}_jobgen.{#}.log" maxArchiveFiles="7" archiveEvery="Day" archiveNumbering="Rolling"/>
		<target name="SQDASA" xsi:type="File" fileName="C:/Logs/debug/${logger}.log" layout="${remoteLayout}" encoding="utf-8"
			archiveFileName="${logBaseDir}/${logger}.{#}.log" maxArchiveFiles="7" archiveEvery="Day" archiveNumbering="Rolling"/>

		<!-- just for debug purposes via viewer -->
		<target xsi:type="NLogViewer" name="viewer" address="udp4://127.0.0.1:9999"/>
		<target xsi:type="Chainsaw" name="chainsaw" address="udp4://127.0.0.1:7071" />
	</targets>

	<targets>
		<target name="eventLog" xsi:type="EventLog" source="${companyName} ${productTitle} ${appName}" layout="${message}${newline}${exception:format=tostring}"/>
	</targets>

	<rules>
		<logger name="*" minlevel="${logFileLevel}" writeTo="logfile"/>
		<logger name="*" level="${eventLogLevel}" writeTo="eventLog"/>
		<logger name="CCC_Custom_SQDASA" minlevel="${logFileLevel}" writeTo="SQDASA"/>

		<!-- just for debug purposes via viewer
		<logger name="*" minlevel="Trace" writeTo="viewer"/>
		<logger name="*" minlevel="Trace" writeTo="chainsaw"/>
		-->

		<!-- Debug logger definitions for trace log
		<logger name="*" minlevel="Trace" writeTo="debug"/>
		<logger name="SqlLog" minlevel="Trace" writeTo="sqllogfile"/>
		<logger name="ObjectLog" minlevel="Trace" writeTo="objectlogfile"/>
		<logger name="JobGenLog" minlevel="Trace" writeTo="jobgenlogfile"/>
		-->
	</rules>

	<include file="${basedir}/custom-log-targets.config" ignoreErrors="true"/>

</nlog>

