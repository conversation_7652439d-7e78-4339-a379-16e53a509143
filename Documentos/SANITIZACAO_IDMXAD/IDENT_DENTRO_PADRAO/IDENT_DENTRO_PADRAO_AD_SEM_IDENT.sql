SELECT 
    ad.samaccountname AS conta_AD,
    ad.UID_ADSAccount
FROM 
    ADSAccount ad
WHERE 
    -- Contas sem identidade associada (UID_Person é NULL)
    ad.UID_Person IS NULL
    -- Filtra para credenciais dentro do padrão
    AND LEFT(ad.samaccountname, 1) IN ('F', 'T', 'M')
    AND LEN(ad.samaccountname) = 12
    AND ISNUMERIC(SUBSTRING(ad.samaccountname, 2, 11)) = 1
ORDER BY 
    ad.samaccountname;
