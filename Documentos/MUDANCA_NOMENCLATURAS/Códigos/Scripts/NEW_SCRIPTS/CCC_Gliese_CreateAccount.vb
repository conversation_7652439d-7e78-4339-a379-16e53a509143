Public Sub CCC_Gliese_CreateAccount(ByVal identificacao As String, ByVal matricula As String, ByVal nome As String, ByVal funcao As String, ByVal setor As String, ByVal posto As String, ByVal unidadeRealizacaoExame As String, ByVal email As String, ByVal empresa As String, ByVal cpf As String)
	Dim f = Session.SqlFormatter
    Dim personManagerId = identificacao
	' Get UID_Person
    Dim personManagerAdsAccountQuery = f.UidComparison("CentralAccount", personManagerId)
    Dim personManagerAdsAccountCol As IEntityCollection = Session.Source().GetCollection(Query.From("Person").Where(personManagerAdsAccountQuery).SelectNonLobs)
    Dim personManagerAdsAccount As IEntity = personManagerAdsAccountCol(0)
	Dim org As String = "66f3cda1-0081-48da-8053-26e974f02bf4"
	Dim org1 As String = "2f80bc47-1eac-408b-9d94-eb2c62bea1b5"
	Dim org2 As String = "595002f2-13c1-44c3-aa20-15b1ac3cd792"
	Dim org3 As String = "0a643d82-1cd2-43c8-956f-db2c38064227"
	Dim org4 As String = "3f475cbd-445d-42cc-a5d0-df66a72416f0"
	Dim org5 As String = "0cbb8ab2-f319-4ede-97b2-b0432c5fd4e7"
	Dim org6 As String = "4075070b-7a06-4452-a460-d4e57da40a11"
	Dim org7 As String = "2be0d2a0-daa5-413e-8528-88574422b83e"
	Dim org8 As String = "9ede565e-9ff8-4084-b979-31a4fe655160"
	Dim org9 As String = "f4e06ba7-54c4-419d-8f50-551d30cac5fb"
	Dim org10 As String = "98d97a50-ad3d-4150-a2a7-2ce383ac5789"
	Dim org11 As String = "1b127b1d-7ae8-4b88-ab70-5838e5476297"
	Dim org12 As String = "d585a00e-7436-4cb6-98d2-ef8ccd437956"
	Dim org13 As String = "cafa0173-d528-4127-89b2-7bb64278096b"
	Dim org14 As String = "da2b9ad2-a94c-4743-81f9-aa2b0ddfbcf0"
	Dim org15 As String = "67d62c62-a910-4098-8dbc-485c87f6afca"
	Dim org16 As String = "1c846dca-7038-4e69-b78e-7a0b207f9fcd"
	
	Dim personManagerAdsAccountQuery2 = f.AndRelation(f.UidComparison("UID_Person", personManagerAdsAccount.GetValue("UID_Person").String), f.OrRelation(f.UidComparison("UID_Org", org),(f.UidComparison("UID_Org", org1)),(f.UidComparison("UID_Org", org2)),(f.UidComparison("UID_Org", org3)),(f.UidComparison("UID_Org", org4)),(f.UidComparison("UID_Org", org5)),(f.UidComparison("UID_Org", org6)),(f.UidComparison("UID_Org", org7)),(f.UidComparison("UID_Org", org8)),(f.UidComparison("UID_Org", org9)),(f.UidComparison("UID_Org", org10)),(f.UidComparison("UID_Org", org11)),(f.UidComparison("UID_Org", org12)),(f.UidComparison("UID_Org", org13)),(f.UidComparison("UID_Org", org14)),(f.UidComparison("UID_Org", org15)),(f.UidComparison("UID_Org", org16))))
    Dim personManagerAdsAccountCol2 As IEntityCollection = Session.Source().GetCollection(Query.From("PersonInOrg").Where(personManagerAdsAccountQuery2).SelectNonLobs)

    Dim funcaoS As String = ""
	Dim setorS As String = ""
	
	If personManagerAdsAccountCol2 IsNot Nothing AndAlso personManagerAdsAccountCol2.Count > 0 Then
		Dim personManagerAdsAccount2 As IEntity = personManagerAdsAccountCol2(0)
		Dim personManagerAdsAccountQuery3 = f.UidComparison("UID_Org", personManagerAdsAccount2.GetValue("UID_Org").String)
		Dim personManagerAdsAccountCol3 As IEntityCollection = Session.Source().GetCollection(Query.From("Org").Where(personManagerAdsAccountQuery3).SelectNonLobs)
		
		If personManagerAdsAccountCol3 IsNot Nothing AndAlso personManagerAdsAccountCol3.Count > 0 Then
			Dim personManagerAdsAccount3 As IEntity = personManagerAdsAccountCol3(0)
			funcaoS = personManagerAdsAccount3.GetValue("CustomProperty01").String ' funcao
			setorS  = personManagerAdsAccount3.GetValue("CustomProperty02").String ' setor
		End If
	End If
	
	
Dim identificacaoS As String = identificacao
Dim matriculaS As String = matricula
Dim nomeS As String = nome

Dim postoS As String = "SED" ' posto
Dim unidadeRealizacaoExameS As String = "SED" ' unidadeRealizacaoExame
Dim emailS As String = email
Dim empresaS As String = "23" ' empresa
Dim cpfS As String = cpf
Dim marcaS As String = unidadeRealizacaoExame
Dim empresaSS As String = "23"
	If marcaS = "Delboni" Then
		empresaS = "31"
		empresaSS = "31,58"
		Else If MarcaS = "Alta" Then
		empresaS = "23"
		empresaSS = "23,98"
		Else If MarcaS = "Image" Then
		empresaS = "54"
		empresaSS = "54"
		Else If MarcaS = "HOSPITAL SANTA PAULA" Then
		empresaS = "31"
		empresaSS = "31"
		Else If MarcaS = "HOSPITAL SÃO LUCAS" Then
		empresaS = "10"
		empresaSS = "10"
		Else If MarcaS = "HOSPITAL DO CARMO" Then
		empresaS = "10"
		empresaSS = "10"
		Else If MarcaS = "HOSPITAL CLINICAS DE NITEROI" Then
		empresaS = "10"
		empresaSS = "10"
		Else If MarcaS = "HOSPITAL AGUAS CLARAS" Then
		empresaS = "55"
		empresaSS = "55"
		Else If MarcaS = "HOPSITAL BRASILIA" Then
		empresaS = "55"
		empresaSS = "55"
		Else If MarcaS = "HOSPITAL MATERNIDADE BRASILIA" Then
		empresaS = "55"
		empresaSS = "55"
		Else If MarcaS = "HOSPITAL 9 JULHO" Then
		empresaS = "23"
		empresaSS = "23"
		Else If MarcaS = "LEFORTE MORUMBI" Then
		empresaS = "31"
		empresaSS = "31"
		Else If MarcaS = "LEFORTE LIBERDADE" Then
		empresaS = "31"
		empresaSS = "31"
		Else If MarcaS = "CRISTOVÃO DA GAMA" Then
		empresaS = "31"
		empresaSS = "31"
		Else If MarcaS = "HOSPITAL MATERNIDADE CHRISTOVAO DA GAMA SANTO ANDRE" Then
		empresaS = "31"
		empresaSS = "31"
		Else If MarcaS = "HOSPITAL CHRISTOVAO DA GAMA DIADEMA (INNVOVA)" Then
		empresaS = "32"
		empresaSS = "32"
		Else If MarcaS = "HOSPITAL BAHIA" Then
		empresaS = "61"
		empresaSS = "61"
		Else If MarcaS = "CL" Then
		empresaS = "18"
		empresaSS = "18"
		Else If MarcaS = "Cytolab" Then
		empresaS = "53"
		empresaSS = "53"
		Else If MarcaS = "Salomão" Then
		empresaS = "67"
		empresaSS = "67"
		Else If MarcaS = "LAVOISIER" Then
		empresaS = "32"
		empresaSS = "32"
		Else If MarcaS = "ÁLVARO" Then
		empresaS = "27"
		empresaSS = "27"
		Else If MarcaS = "Atalaia" Then
		empresaS = "52"
		empresaSS = "52"
		Else If MarcaS = "BRONSTEIN" Then
		empresaS = "16"
		empresaSS = "16"
		Else If MarcaS = "EXAME" Then
		empresaS = "55"
		empresaSS = "55"
		Else If MarcaS = "FRISCHMANN" Then
		empresaS = "51"
		empresaSS = "51"
		Else If MarcaS = "SÉRGIO FRANCO" Then
		empresaS = "10"
		empresaSS = "10"
		Else If MarcaS = "CERPE" Then
		empresaS = "57"
		empresaSS = "57"
		Else If MarcaS = "Lamina" Then
		empresaS = "17"
		empresaSS = "17"
		Else If MarcaS = "Cedilab" Then
		empresaS = "26"
		empresaSS = "26"
		Else If MarcaS = "Image" Then
		empresaS = "54"
		empresaSS = "54"
		Else If MarcaS = "Gaspar" Then
		empresaS = "63"
		empresaSS = "63"
		Else If MarcaS = "PREVILAB" Then
		empresaS = "43"
		empresaSS = "43"
		Else If MarcaS = "DELBONI AURIEMO" Then
		empresaS = "31"
		empresaSS = "31"
		Else If MarcaS = "Valeclin" Then
		empresaS = "71"
		empresaSS = "71"
		Else If MarcaS = "Image" Then
		empresaS = "54"
		empresaSS = "54"
		Else If MarcaS = "GHANEM" Then
		empresaS = "69"
		empresaSS = "69"
		Else If MarcaS = "VITAL BRASIL" Then
		empresaS = "68"
		empresaSS = "68"
		Else If MarcaS = "SÃO CAMILO" Then
		empresaS = "83"
		empresaSS = "83,81,84"
		Else If MarcaS = "LEME" Then
		empresaS = "61"
		empresaSS = "61"
		Else If MarcaS = "Cdpi Leblon" Then
		empresaS = "21"
		empresaSS = "21,64,65"
		Else If MarcaS = "Cdpi SP" Then
		empresaS = "21"
		empresaSS = "21"
		Else If MarcaS = "PADRÃO" Then
		empresaS = "75"
		empresaSS = "75"
		Else If MarcaS = "HEMAT" Then
		empresaS = "85"
		empresaSS = "85"
		Else If MarcaS = "OSWALDO CRUZ" Then
		empresaS = "59"
		empresaSS = "59"
		Else If MarcaS = "ITULAB" Then
		empresaS = "76"
		empresaSS = "76"
		Else If MarcaS = "BIOCLINICO" Then
		empresaS = "79"
		empresaSS = "79"
		Else If MarcaS = "LABPASTEUR" Then
		empresaS = "56"
		empresaSS = "56"
		Else If MarcaS = "CIELAB - CL" Then
		empresaS = "18"
		empresaSS = "18"
		Else If MarcaS = "DELIBERATO" Then
		empresaS = "72"
		empresaSS = "72"
		Else If MarcaS = "SALOMÃO ZOPPI - PANAMBY" Then
		empresaS = "67"
		empresaSS = "67"
		Else If MarcaS = "FOCOS" Then
		empresaS = "13"
		empresaSS = "13"
		Else If MarcaS = "PAT PA TATUAPE" Then
		empresaS = "32"
		empresaSS = "32"
		Else If MarcaS = "MULTIMAGEM" Then
		empresaS = "66"
		empresaSS = "66"
		Else If MarcaS = "CDPI" Then
		empresaS = "64"
		empresaSS = "64,65"
		Else If MarcaS = "CEDIC" Then
		empresaS = "47"
		empresaSS = "47"
		Else If MarcaS = "Gilson Cidrim" Then
		empresaS = "62"
		empresaSS = "62"
		Else If MarcaS = "Antonello" Then
		empresaS = "91"
		empresaSS = "91"
		Else If MarcaS = "Barcellar" Then
		empresaS = "92"
		empresaSS = "92"
		Else If MarcaS = "Cec" Then
		empresaS = "93"
		empresaSS = "93"
		Else If MarcaS = "Exame Sul" Then
		empresaS = "94"
		empresaSS = "94"
		Else If MarcaS = "Lunav" Then
		empresaS = "95"
		empresaSS = "95"
		Else If MarcaS = "Senhor dos Passos" Then
		empresaS = "96"
		empresaSS = "96"
		Else If MarcaS = "Exame NB" Then
		empresaS = "102"
		empresaSS = "102"
		Else If MarcaS = "ALTA EXCELENCIA DIAGNOSTICOS" Then
		empresaS = "23"
		empresaSS = "23,99"
		Else If MarcaS = "ALVARO" Then
		empresaS = "27"
		empresaSS = "27"
		Else If MarcaS = "ÁLVARO" Then
		empresaS = "27"
		empresaSS = "27"
		Else If MarcaS = "BIOMÉDICOS" Then
		empresaS = "79"
		empresaSS = "79"
		Else If MarcaS = "Image" Then
		empresaS = "54"
		empresaSS = "54"
		Else If MarcaS = "CIENTIFICA" Then
		empresaS = "18"
		empresaSS = "18"
		Else If MarcaS = "CIENTIFICA LAB" Then
		empresaS = "18"
		empresaSS = "18"
		Else If MarcaS = "CIENTIFICALAB" Then
		empresaS = "18"
		empresaSS = "18"
		Else If MarcaS = "CL - AL" Then
		empresaS = "18"
		empresaSS = "18"
		Else If MarcaS = "CL - GO" Then
		empresaS = "18"
		empresaSS = "18"
		Else If MarcaS = "CL - MG" Then
		empresaS = "18"
		empresaSS = "18"
		Else If MarcaS = "CL - RJ" Then
		empresaS = "18"
		empresaSS = "18"
		Else If MarcaS = "CL - SP" Then
		empresaS = "18"
		empresaSS = "18"
		Else If MarcaS = "CRISTOVÃO DA GAMA" Then
		empresaS = "97"
		empresaSS = "97"
		Else If MarcaS = "DELBONI AURIEMO - HOSPITAL MAE DE DEUS" Then
		empresaS = "31"
		empresaSS = "31"
		Else If MarcaS = "DELBONI BROOKLIN" Then
		empresaS = "31"
		empresaSS = "31"
		Else If MarcaS = "DELL" Then
		empresaS = "31"
		empresaSS = "31"
		Else If MarcaS = "GRUPO EXAME" Then
		empresaS = "94"
		empresaSS = "94"
		Else If MarcaS = "H9J" Then
		empresaS = "23"
		empresaSS = "23,99"
		Else If MarcaS = "HBA" Then
		empresaS = "98"
		empresaSS = "98"
		Else If MarcaS = "ITLAB" Then
		empresaS = "76"
		empresaSS = "76"
		Else If MarcaS = "LABORATÓRIO ALVARO" Then
		empresaS = "67"
		empresaSS = "67"
		Else If MarcaS = "LABORATÓRIO GASPAR" Then
		empresaS = "63"
		empresaSS = "63"
		Else If MarcaS = "LABORATÓRIO SANTA LUZIA" Then
		empresaS = "71"
		empresaSS = "71"
		Else If MarcaS = "LABPASTER" Then
		empresaS = "56"
		empresaSS = "56"
		Else If MarcaS = "LÂMINA" Then
		empresaS = "17"
		empresaSS = "17"
		Else If MarcaS = "MULTI IMAGEM" Then
		empresaS = "66"
		empresaSS = "66"
		Else If MarcaS = "PASTEUR" Then
		empresaS = "56"
		empresaSS = "56"
		Else If MarcaS = "PREMIUM" Then
		empresaS = "23"
		empresaSS = "23,99"
		Else If MarcaS = "SALOMÃO ZOPPI" Then
		empresaS = "67"
		empresaSS = "67"
		Else If MarcaS = "SERGIO FRANCO" Then
		empresaS = "10"
		empresaSS = "10"
		Else If MarcaS = "UNIMAGEM" Then
		empresaS = "44"
		empresaSS = "44"
		Else If MarcaS = "GENE ONE" Then
		empresaS = "78"
		empresaSS = "78"
		Else If MarcaS = "VITA" Then
		empresaS = "28"
		empresaSS = "28"
		Else If MarcaS = "Delboni Prime" Then
		empresaS = "58"
		empresaSS = "58,31"
		
	End If
	
' Define the JSON data to send  
'Dim jsonData As String = "{ ""usuario"": { ""identificacao"": " + user + ", ""menus"": [" + groups + "] } }" 
Dim jsonData As String = String.Format("{{ ""usuario"": {{ ""identificacao"": ""{0}"", ""matricula"": ""{1}"", ""nome"": ""{2}"", ""funcao"": ""{3}"", ""setor"": ""{4}"", ""posto"": ""{5}"", ""unidadeRealizacaoExame"": ""{6}"", ""email"": ""{7}"", ""empresaInterna"": ""{8}"", ""cpf"": ""{9}"", ""mnemonicoPosto"": ""SED"", ""dataExpiracao"": ""21112025"", ""senha"": ""{0}"", ""empresasIncorporadas"": [""{10}""] }} }}", identificacaoS, matriculaS, nomeS, funcaoS, setorS, postoS, unidadeRealizacaoExameS, emailS, empresaS, cpfS, empresaSS) 
  
' Create a new WebClient object  
Dim client As New System.Net.WebClient()  
  
' Set the content type header to indicate JSON data  
client.Headers.Add("Content-Type", "application/json")
client.Headers.Add("apikey", "IQRpKNKodzLzAp02J1hwYCkBDRADCwXL")
  
' Send the POST request and get the response data  
Dim responseBytes As Byte() = client.UploadData("https://api.godigibee.io/pipeline/dasa/v1/gliese-usuario", "POST", System.Text.Encoding.ASCII.GetBytes(jsonData))  
Dim responseString As String = System.Text.Encoding.ASCII.GetString(responseBytes)  
  
' Display the response data  

Dim clientU As New System.Net.WebClient()  
clientU.Headers.Add("Content-Type", "application/json")
clientU.Headers.Add("apikey", "IQRpKNKodzLzAp02J1hwYCkBDRADCwXL")
System.Threading.Thread.Sleep(20000)  
Dim jsonDataU As String = String.Format("{{ ""usuario"": {{ ""identificacao"": ""{0}"", ""matricula"": ""{1}"", ""nome"": ""{2}"", ""funcao"": ""{3}"", ""setor"": ""{4}"", ""posto"": ""{5}"", ""unidadeRealizacaoExame"": ""{6}"", ""email"": ""{7}"", ""empresaInterna"": ""{8}"", ""cpf"": ""{9}"", ""mnemonicoPosto"": ""SED"", ""dataExpiracao"": ""21112025"", ""empresasIncorporadas"": [""{10}""] }} }}", identificacaoS, matriculaS, nomeS, funcaoS, setorS, postoS, unidadeRealizacaoExameS, emailS, empresaS, cpfS, empresaSS) 
Dim responseBytesU As Byte() = clientU.UploadData("https://api.godigibee.io/pipeline/dasa/v1/gliese-usuario", "PATCH", System.Text.Encoding.ASCII.GetBytes(jsonDataU))  
Dim responseStringU As String = System.Text.Encoding.ASCII.GetString(responseBytesU) 
 
End Sub