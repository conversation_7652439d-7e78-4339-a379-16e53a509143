Public Sub CCC_Gliese_UpdatePassword(ByVal UID_UNSAccount As String, ByVal encryptedPassword As String, ByVal UID_Object As String)

Dim user As String = UID_UNSAccount
Dim objectSS As String = UID_Object
If objectSS <> "" Then
	objectSS = objectSS.Replace("GLIESE_", "")
	user = objectSS
End If
	' VID_Write2Log("E:\OneIM\Gliese.txt", "ObjectGuid from ByVal:" & " " & ObjectGuid)
	' VID_Write2Log("E:\OneIM\Gliese.txt", "Password from ByVal:" & " " & encryptedPassword)
	
	Dim password = Session.Decrypt(encryptedPassword)
	
	' VID_Write2Log("E:\OneIM\Gliese.txt", "Decrypted password:" & " " & password)
		password = password.ToUpper()
	Dim jsonData As String = String.Format("{{ ""isConsulta"": false, ""usuario"": {{ ""identificacao"": ""{0}"", ""senha"": ""{1}"" }} }}", user, password) 

	'msgbox(jsonData)
	
	Dim client As New System.Net.WebClient()  

	client.Headers.Add("Content-Type", "application/json")
	client.Headers.Add("apikey", "IQRpKNKodzLzAp02J1hwYCkBDRADCwXL")
	Dim responseBytes As Byte() = client.UploadData("https://api.godigibee.io/pipeline/dasa/v1/gliese-usuario", "PATCH", System.Text.Encoding.ASCII.GetBytes(jsonData))  
	Dim responseString As String = System.Text.Encoding.ASCII.GetString(responseBytes)  
	' VID_Write2Log("E:\OneIM\Gliese.txt", "Change password response for" & " " & user & ": " & responseString)
	
End Sub