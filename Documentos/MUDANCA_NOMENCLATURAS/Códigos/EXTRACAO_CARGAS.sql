WITH RankedEntries AS (
    SELECT 
        CentralAccount, 
		PersonnelNumber,
        preferredname, 
        personaltitle, 
        EntryDate,
        ROW_NUMBER() OVER (PARTITION BY personaltitle ORDER BY EntryDate DESC) AS rn
    FROM Person
    WHERE personaltitle IN ('Enfermeiro I',
'Enfermeiro II',
'Enfermeiro III',
'Enfermeiro Lider II',
'Enfermeiro Premium I',
'Enfermeiro Premium II',
'Enfermeiro Premium III',
'Enfermeiro RDI',
'Tecnico Enfermagem I',
'Tecnico Enfermagem RDI I',
'Tecnico Enfermagem RDI II',
'Tecnico Enfermagem RDI III',
'Tecnico Enfermagem RDI IV',
'Tecnico Enfermagem RDI Premium I',
'Tecnico Enfermagem RDI Premium II',
'Tecnico Enfermagem RDI Premium III',
'Tecnico Enfermagem RDI Premium IV',
'Técnico Exame I',
'Técnico Exame RDI III',
'Técnico Imagem I',
'Técnico Imagem II',
'Técnico Imagem III',
'Técnico Imagem Premium I',
'Técnico Radiologia I',
'Técnico Radiologia II',
'Técnico Radiologia Premium I',
'Técnico Radiologia Premium II')
      AND EntryDate > '2024-08-05 00:00:00'
      AND isinactive = 0 -- Filtra apenas usuários ativos
)
SELECT 
		CentralAccount,
		PersonnelNumber, 
        preferredname, 
        personaltitle, 
        EntryDate
FROM RankedEntries
WHERE rn <= 5;