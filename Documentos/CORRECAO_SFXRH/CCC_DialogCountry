' Verifica se o campo CCC_DialogCountry não está vazio
If Not String.IsNullOrEmpty($CCC_DialogCountry$) Then
    ' Declaração de variável para armazenar o UID_DialogCountry
    Dim uidCountry As String = CStr(Connection.GetSingleProperty("DialogCountry", "UID_DialogCountry", "NationalCountryName = '" & $CCC_DialogCountry$ & "'"))

    ' Verifica se encontrou o UID_DialogCountry e preenche o campo
    If Not String.IsNullOrEmpty(uidCountry) Then
        ' Atualiza o campo UID_DialogCountry com o valor encontrado
        Entity.PutValue("UID_DialogCountry", uidCountry)
    Else
        ' Se não encontrou, define UID_DialogCountry como vazio
        Entity.PutValue("UID_DialogCountry", Nothing)
    End If
End If
