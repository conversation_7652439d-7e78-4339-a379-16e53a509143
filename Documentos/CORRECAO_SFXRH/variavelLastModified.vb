Imports VI.Projector.Connection

Dim credencial As String = "$PersonnelNumber$"

Dim timestamp As ISystemObject = SystemObject.Connection.QueryObject( _
    SystemQuery.From("Person") _
        .Select("CustomProperty08") _
        .Filter(String.Format("PersonnelNumber='{0}'", credencial)) _
).Result.FirstOrDefault()

Dim lastModified As String = timestamp.GetValue("CustomProperty08").AsString

If Not lastModified Is Nothing Then
    Return lastModified
End If
