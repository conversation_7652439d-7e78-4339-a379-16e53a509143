' Verifica se o campo CCC_ProfitCenter não está vazio
If Not String.IsNullOrEmpty($CCC_ProfitCenter$) Then
    ' Declaração de variável para armazenar o UID_ProfitCenter
    Dim uidProfitCenter As String = CStr(Connection.GetSingleProperty("ProfitCenter", "UID_ProfitCenter", "AccountNumber = '" & $CCC_ProfitCenter$ & "'"))

    ' Verifica se encontrou o UID_ProfitCenter e preenche o campo
    If Not String.IsNullOrEmpty(uidProfitCenter) Then
        ' Atualiza o campo UID_ProfitCenter com o valor encontrado
        Entity.PutValue("UID_ProfitCenter", uidProfitCenter)
    Else
        ' Se não encontrou, define UID_ProfitCenter como vazio
        Entity.PutValue("UID_ProfitCenter", Nothing)
    End If
End If
